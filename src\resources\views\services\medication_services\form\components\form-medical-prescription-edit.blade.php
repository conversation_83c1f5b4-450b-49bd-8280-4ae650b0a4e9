<div class="title">
    <i class="dropdown icon"></i>
    Receta médica
    <i class="info circle icon rx-help"></i>
</div>

<div class="content" id="rx-form">
    <div class="rx-container">
        <div class="rx-block">
            {{-- Ubicación --}}
            <div class="rx-location location-group">
                <div class="three fields">
                    <div class="field">
                        <label>Provincia</label>
                        <div class="province ui search selection dropdown">
                            <input type="hidden" id="province_medical_prescription" name="province_medical_prescription"
                                class="minus_font province_value"
                                value="{{($medication_service->province_medical_prescription ?? $medication_service->province_controlled_medication) ?? ''}}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Cantón</label>
                        <div
                            class="canton ui search selection dropdown @if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA) disabled @endif">
                            <input type="hidden" id="canton_medical_prescription" name="canton_medical_prescription"
                                class="minus_font canton_value"
                                value="{{($medication_service->canton_medical_prescription ?? $medication_service->canton_controlled_medication) ?? ''}}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Distrito</label>
                        <div
                            class="district ui search selection dropdown  @if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA) disabled @endif">
                            <input type="hidden" id="district_medical_prescription" name="district_medical_prescription"
                                class="minus_font district_value"
                                value="{{($medication_service->district_medical_prescription ?? $medication_service->district_controlled_medication) ?? ''}}">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>


                    <div class="two wide field" style="margin-top:25px;">
                        <button type="button" class="ui red small icon fluid button" id="rx-clear-location">
                            <i class="undo icon"></i> Limpiar
                        </button>
                    </div>

                </div>
            </div>

            {{-- Lista dinámica de medicamentos --}}
            <div class="ui grid" id="rx-list" style="margin-top:15px;">
                @foreach($medical_prescriptions as $index => $m)
                    @php $i = $index + 1; @endphp

                    <div class="rx-item ui grid" data-index="{{ $i }}">
                        <div class="two column row" style="margin-top:15px;">
                            <div class="column">
                                <h4 class="medicine-title" data-alias="receta_medica">Medicamento N° {{ $i }}</h4>
                            </div>
                            @if($i > 1)
                                <div class="column right aligned">
                                    <a class="ui red small icon basic right floated button rx-remove" style="margin-top:5px;">
                                        <i class="remove icon"></i>
                                    </a>
                                </div>
                            @endif
                        </div>

                        {{-- OJO: aquí ya no usamos .rx-item otra vez --}}
                        <div class="ui grid vademecum-group" data-index="{{ $i }}">
                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Mólecula</label>
                                        <div class="ui search selection dropdown rx-molecule moleculaVademecum"
                                            id="rx-molecule-{{ $i }}">
                                            <input type="hidden" name="rx[{{ $i }}][molecule]"
                                                value="{{ $m->molecula ?? '' }}" class="moleculaVademecum_value"
                                                onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona una molécula</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Tipo</label>
                                        <div class="ui search selection dropdown rx-type tipoVademecum "
                                            id="rx-type-{{ $i }}">
                                            <input type="hidden" name="rx[{{ $i }}][type]" value="{{ $m->tipo ?? '' }}"
                                                class="tipoVademecum_value"
                                                onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona un tipo</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Descripción</label>
                                        <div class="ui search selection dropdown rx-desc descripVademecumDropdown "
                                            id="rx-desc-{{ $i }}">
                                            <input type="hidden" name="rx[{{ $i }}][descrip]"
                                                value="{{ $m->descrip ?? '' }}">
                                            <i class="dropdown icon"></i>
                                            <div class="default text"></div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="required field">
                                        <label>Código</label>
                                        <textarea class="textarea-expand" name="codigo-vademecumrx[{{ $i }}]"
                                            readonly>{{ $m->codigo ?? '' }}</textarea>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="field">
                                        <label>Casa</label>
                                        <textarea class="textarea-expand" name="casa-vademecumrx[{{ $i }}]"
                                            readonly>{{ $m->casa ?? '' }}</textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="required field">
                                        <label>Duración del tratamiento</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][treatment_duration]"
                                            rows="2">{{ $m->treatment_duration ?? '' }}</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Frecuencia</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][frequency]"
                                            rows="2">{{ $m->frequency ?? '' }}</textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Dosis / vía</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][dosage]"
                                            rows="2">{{ $m->dosage ?? '' }}</textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Cantidad (letras)</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][qty_letters]"
                                            rows="2">{{ $m->quantity_letters ?? '' }}</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Cantidad (números)</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][qty_numbers]" rows="2"
                                            oninput="this.value=this.value.replace(/[^0-9]/g,'');">{{ $m->quantity_numbers ?? '' }}</textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Notas</label>
                                        <textarea class="auto-resize" name="rx[{{ $i }}][notes]"
                                            rows="2">{{ $m->notes ?? '' }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                @endforeach
            </div>

            <div class="fields">
                <div class="field two wide required" style="margin-top:25px;">
                    <a class="ui basic small icon blue fluid button" id="rx-dup">
                        <i class="add icon"></i>Agregar
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        // (1) Inicializa dropdowns SOLO en el scope pasado
        function initDropdowns($scope) {
            ($scope || $(document)).find('.ui.selection.dropdown').dropdown({
                fullTextSearch: true,
                ignoreDiacritics: true
            });
        }

        // (2) Siguiente índice
        function nextIndex() {
            const $list = $('#rx-list');
            const last = $list.find('.rx-item:last').data('index') || 0;
            return last + 1;
        }

        // (3) Reindexa names/ids y título
        function reindexNode($node, oldIndex, newIndex) {
            $node.attr('data-index', newIndex);
            $node.find('.medicine-title').text('Medicamento N° ' + newIndex);

            // name="rx[old][...]" -> "rx[new][...]"
            $node.find('input[name], textarea[name], select[name]').each(function () {
                const name = $(this).attr('name');
                if (name) $(this).attr('name', name.replace(/\[\d+\]/, '[' + newIndex + ']'));
            });

            // id="...-old" -> "...-new" (si termina en -número)
            $node.find('[id]').each(function () {
                const id = $(this).attr('id');
                if (id && /-\d+$/.test(id)) $(this).attr('id', id.replace(/-\d+$/, '-' + newIndex));
            });
        }

        // (4) Limpia valores del clon
        function clearValues($node) {
            $node.find('textarea').val('');
            $node.find('input[type="text"], input[type="hidden"]').val('');
            $node.find('.ui.selection.dropdown').each(function () {
                // quita clases de estado que puedan venir copiadas
                $(this).removeClass('active visible filtered');
                try { $(this).dropdown('clear'); } catch (e) { }
            });
        }

        // (5) Agregar (clonar “sin eventos” y re-inicializar)
        $('#rx-dup').on('click', function () {
            const $list = $('#rx-list');
            const $last = $list.find('.rx-item:last');
            if (!$last.length) return;

            const oldIndex = $last.data('index') || 0;
            const newIndex = nextIndex();

            // ❗ Clon limpio (sin eventos): usar outerHTML → jQuery crea nuevos nodos sin handlers
            const $clone = $($last.prop('outerHTML'));

            reindexNode($clone, oldIndex, newIndex);
            clearValues($clone);

            // Asegura que cualquier estado de dropdown no se herede
            $clone.find('.ui.dropdown').each(function () {
                try { $(this).dropdown('destroy'); } catch (e) { }
                $(this).removeClass('active visible filtered');
            });

            $list.append($clone);
            initDropdowns($clone); // Inicializa SOLO el clon
        });

        // (6) Eliminar item
        $(document).on('click', '.rx-remove', function () {
            $(this).closest('.rx-item').remove();
        });

        // (7) Limpia ubicación
        $('#rx-clear-location').on('click', function () {
            $('.province input[type=hidden], .canton input[type=hidden], .district input[type=hidden]').val('');
            $('.province, .canton, .district').dropdown('clear');
        });

        // (8) Reemplaza los inline onchange por delegados (evita handlers duplicados)
        $(document).on('change', '.moleculaVademecum_value', function () {
            handleMoleculaChange($(this).closest('.vademecum-group'));
        });
        $(document).on('change', '.tipoVademecum_value', function () {
            handleTipoChange($(this).closest('.vademecum-group'));
        });

        // Inicializa los existentes al cargar
        initDropdowns();




    });

    $(document).ready(function () {
        setTimeout(function () {
           
        }, 11000); // espera 1s para que Semantic UI cargue los menús
    });


</script>