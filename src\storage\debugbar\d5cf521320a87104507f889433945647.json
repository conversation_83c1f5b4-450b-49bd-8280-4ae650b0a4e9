{"__meta": {"id": "d5cf521320a87104507f889433945647", "datetime": "2025-09-16 23:21:41", "utime": 1758086501.162296, "method": "GET", "uri": "/servicio/518948/medication_services", "ip": "**********"}, "php": {"version": "7.2.34", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758086468.440408, "end": 1758086501.162339, "duration": 32.72193098068237, "duration_str": "32.72s", "measures": [{"label": "Booting", "start": 1758086468.440408, "relative_start": 0, "end": 1758086486.07675, "relative_end": 1758086486.07675, "duration": 17.63634204864502, "duration_str": "17.64s", "params": [], "collector": null}, {"label": "Application", "start": 1758086483.336987, "relative_start": 14.896579027175903, "end": 1758086501.16234, "relative_end": 9.5367431640625e-07, "duration": 17.825352907180786, "duration_str": "17.83s", "params": [], "collector": null}]}, "memory": {"peak_usage": 2097152, "peak_usage_str": "2MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "services.medication_services.form.form (resources/views/services/medication_services/form/form.blade.php)", "param_count": 12, "params": ["activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-insured-details (resources/views/services/medication_services/form/components/form-insured-details.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-case-details (resources/views/services/medication_services/form/components/form-case-details.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-supplier-details (resources/views/services/medication_services/form/components/form-supplier-details.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-diagnosis (resources/views/services/medication_services/form/components/form-diagnosis.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-controlled-medication-formula (resources/views/services/medication_services/form/components/form-controlled-medication-formula.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.pharmacies (resources/views/services/medication_services/form/components/pharmacies.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}, {"name": "services.medication_services.form.components.form-medical-prescription-edit (resources/views/services/medication_services/form/components/form-medical-prescription-edit.blade.php)", "param_count": 190, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data", "__currentLoopData", "medicine", "index", "loop"], "type": "blade"}, {"name": "layouts.main (resources/views/layouts/main.blade.php)", "param_count": 186, "params": ["obLevel", "__env", "app", "PAYMENT_METHODS", "ESTADOS_PROCESO", "OCCUPATION_CATEGORIES", "PERIODICITYT", "REGIONAL", "DOMINANCE", "CONCAUSAS", "MONEY_TYPE", "WORK_MODALITY", "OPTION_ASEGUREMENT", "INSTITUTIONAL_SECTOR", "STATE_FRACTION_IT", "VARIATIONS_TYPE", "CALENDAR_PERIOD", "TYPE_IT", "TYPE_ATTENTION", "TYPE_INCOME", "GLOSA_REASONS_STD", "SENDERS", "ORIGINS", "ORIGINS_AT", "ORIGINS_PCL", "LATERALITY", "DIAGNOSTIC_STATUS", "ROOT_CAUSE", "TYPE_DISABILITY", "RHB_FORMATS", "RHB_TERMS", "REJECTION_CAUSALS_PCL", "REJECTION_CAUSALS_OLD_PCL", "TYPE_OF_PAYMENT", "VALIDATION_RESULT", "BENEFICIARY", "ESTADO_PAGO", "TYPE_CONSTANCY", "LATERALIDAD", "CLASIFICACION", "CARE_LEVEL", "CARE_MODALITY", "ENABLED_STATES_COBRO", "RHB_TREATMENTS", "RHB_PURPOSES", "RHB_DURATIONS", "RHB_PERFOMANCES", "RHB_INSTRUMENTS", "RHB_ROLE", "RECOMMENDATIONS_FORMATS", "RECOMMENDATIONS_TERMS", "CATEGORIZATION_INCIDENT", "SEVERITY", "ACTUARIAL_SEVERITY", "OPTIONS", "TREATMENT_PURPOSE", "PATIENT_PROGNOSIS", "HIGH_COST", "REINCORPORATION_CLASS", "ACCIDENT_TYPES", "AGENTS", "AGENTS_INFO", "INJURIES", "WORK_MODES", "WORKPLACE_LOCATIONS", "DCRT_GRADES", "DCRT_TYPES", "IT_ORIGINS", "IT_DISABILITY_TYPES", "QUALIFIERS", "CONTROVERSY_AUTHORS", "DOCUMENTS", "USER_TYPES", "ADDRESS_KEYS", "INFORMATION_SOURCES", "INFORMATION_SOURCES_PCL", "INFORMATION_SOURCES_RECOMMENDATIONS", "INFORMATION_SOURCES_RHB", "INFORMATION_SOURCES_MAPFRE", "GENDERS", "CIVIL_STATUS", "TIPO_JORNADA_LABORAL", "REQUEST_REASON", "SPECIALTY", "SCHOOL_LEVELS", "SCHOOLING", "AFFILIATION_TYPES", "DOC_TYPES", "STATES_REINTEGRATE", "PERIODICITY_RECEIPTS", "STATES_RESULT_REINTEGRATE", "CONSULTATION_CHANNELS", "ORIGIN_DIAGNOSIS", "ORIGIN_DIAGNOSIS_UPDATED", "TYPE_CASE", "TYPE_DIAGNOSIS", "DISABILITY_TYPE", "ATTENTION_MODE", "CLIENT_TYPES", "TYPE_CURRENCY", "S3_DOCUMENTS_MNK", "BOARDS_DATA", "BOARDS", "EPS_LIST", "AFP_LIST", "ARL_LIST", "EPS_DATA", "EPS_DATA_ATTRIBUTE", "AFP_DATA_ATTRIBUTE", "ARL_DATA_ATTRIBUTE", "AFP_DATA", "ARL_DATA", "SEGURO_DATA", "MIN_WAGES", "RISK_FACTOR_ORIGIN_CALIFICATION", "CATEGORICAL_INFO_PQR", "INABILITY_ORIGIN", "REHABILITATION_DETAILS", "PENDING_CLOSURE_CAUSE", "EVENT_TYPE", "FUNCTIONAL_PROGNOSIS", "INABILITY_TYPE", "QUALIFICATION_REASON_AUDIT", "IT_RESULT_INABILITY", "IT_REASON_INABILITY", "RADICATION_TYPE", "MDI_MISSING_DOCUMENTS", "GENERAL_INFORMATION_FIELDS", "TRAMITE_INFORMATION_FIELDS", "PAYMENT_INFORMATION_FIELDS", "DETAIL_PERIODS_FIELDS", "TYPE_IPS", "ROOM_NAME_TUTELAGE_IT", "SECTION_NAME_TUTELAGE_IT", "OCCUPATIONS", "CONDICION", "CONDICION_SPECIAL", "JURISDICTION_TUTELAGE_IT", "SPECIALTY_TUTELAGE_IT", "COURT_NUMBER_TUTELAGE_IT", "SPECIALITY", "REJECTION_CAUSALS_TEMP", "MEETING_ATING_OF", "MEETING_TYPE_EVENT", "MEETING_INTERESTED_PARTY_THAT_DISPUTES", "IT_DOCUMENTS", "MEETING_ATTACHED_DOCUMENTS", "MEETING_AVERAGE_COSTS", "MEETING_APPEAL_STATUS", "MEETING_JRCI_AVERAGE_COST", "MEETING_PRONOUNCEMENT", "MEETING_JRCI_DICTUM_AVERAGE_COST", "COLPENSIONES_GPA_ORIGINS", "ENTITY_NAME_PERITAJE", "CATEGORIES_AND_CAUSAL", "SMMLV", "REJECTION_CAUSALS", "REJECTION_CAUSALS_PB", "BANKS", "MNK_SERVICES", "IT_DOCUMENT_TYPE_SOL_DOCS", "RECEIPT_STATE", "QUOTATIONS_STATE", "OCCUPATION_GROUPS", "TYPE_RECEIPT", "SPECIALTIES", "TYPE_PERMANENT_DISABILITY", "TYPE_BENEFICIARY", "HIGH_RISK_ECONOMIC_ACTIVITIES", "STUDY_TYPE", "ANATOMICAL_ZONE", "SPECIFIC_BODY_PART", "TEST_TYPE", "errors", "activity", "affiliate", "id", "policy_sort", "activity_economic_name", "gis", "medication_service", "diagnostics", "medical_prescriptions", "controlled_medications", "medications", "data"], "type": "blade"}]}, "route": {"uri": "GET servicio/{id}/medication_services", "middleware": "web, App\\Http\\Middleware\\CheckClientAccess, renew.password", "domain": "{cpath}.renapp.com", "controller": "App\\Http\\Controllers\\Services\\MedicationServicesController@form", "namespace": "App\\Http\\Controllers", "prefix": null, "where": [], "file": "app/Http/Controllers/Services/MedicationServicesController.php:110-206"}, "queries": {"nb_statements": 58, "nb_failed_statements": 0, "accumulated_duration": 8.22958, "accumulated_duration_str": "8.23s", "statements": [{"sql": "select * from `users` where `id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 46, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 49, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 70, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13871, "duration_str": "138.71ms", "stmt_id": "/app/Http/Middleware/RequestLoggerMiddleware.php:28", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 22}, {"index": 48, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 51, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 72, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14097, "duration_str": "140.97ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:22", "connection": "ebdb"}, {"sql": "select * from `user_clients` where `client_id` = '3' and `user_id` = '18' limit 1", "type": "query", "params": [], "bindings": ["3", "18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 25}, {"index": 47, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 50, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 71, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13581000000000001, "duration_str": "135.81ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:25", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 113}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13418, "duration_str": "134.18ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:113", "connection": "ebdb"}, {"sql": "select * from `activities` where `client_id` = '3' and `id` = '518948' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3", "518948"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 116}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15744, "duration_str": "157.44ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:116", "connection": "ebdb"}, {"sql": "select * from `activities` where `client_id` = '3' and `id` = '464632' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3", "464632"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 119}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13436, "duration_str": "134.36ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:119", "connection": "ebdb"}, {"sql": "select * from `activities` where `client_id` = '3' and `id` = '464631' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3", "464631"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 122}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13415000000000002, "duration_str": "134.15ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:122", "connection": "ebdb"}, {"sql": "select * from `gis_sort` where `activity_id` = '464631' limit 1", "type": "query", "params": [], "bindings": ["464631"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 123}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.21428, "duration_str": "214.28ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:123", "connection": "ebdb"}, {"sql": "select * from `activities` where `id` = '464299' and `activities`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["464299"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 126}, {"index": 21, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 24, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 58, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 61, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 82, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13258, "duration_str": "132.58ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:126", "connection": "ebdb"}, {"sql": "select * from `policy_sorts` where `policy_sorts`.`activity_id` = '464299' and `policy_sorts`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["464299"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 127}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14386000000000002, "duration_str": "143.86ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:127", "connection": "ebdb"}, {"sql": "select * from `medications` where `medications`.`activity_id` = '518948' and `medications`.`activity_id` is not null limit 1", "type": "query", "params": [], "bindings": ["518948"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 137}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13896, "duration_str": "138.96ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:137", "connection": "ebdb"}, {"sql": "select * from `medication_service_diagnostics` where `medication_service_diagnostics`.`medication_service_sort_id` = '184' and `medication_service_diagnostics`.`medication_service_sort_id` is not null", "type": "query", "params": [], "bindings": ["184"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 140}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13877, "duration_str": "138.77ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:140", "connection": "ebdb"}, {"sql": "select * from `medication_service_medical_prescriptions` where `medication_service_medical_prescriptions`.`medication_service_sort_id` = '184' and `medication_service_medical_prescriptions`.`medication_service_sort_id` is not null", "type": "query", "params": [], "bindings": ["184"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 143}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13932, "duration_str": "139.32ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:143", "connection": "ebdb"}, {"sql": "select * from `medication_service_controlled_medications` where `medication_service_controlled_medications`.`medication_service_sort_id` = '184' and `medication_service_controlled_medications`.`medication_service_sort_id` is not null", "type": "query", "params": [], "bindings": ["184"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "backtrace": [{"index": 17, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 146}, {"index": 25, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 28, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 65, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 71, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 86, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14301, "duration_str": "143.01ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:146", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `affiliates`.`id` = '1352454' and `affiliates`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1352454"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": null, "name": "/app/Http/Controllers/Services/MedicationServicesController.php", "line": 149}, {"index": 26, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 29, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 63, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 66, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 87, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14358, "duration_str": "143.58ms", "stmt_id": "/app/Http/Controllers/Services/MedicationServicesController.php:149", "connection": "ebdb"}, {"sql": "select * from `affiliates` where `affiliates`.`id` = '1352454' and `affiliates`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1352454"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": "view", "name": "services.medication_services.form.form", "line": 18}, {"index": 25, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 30, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 33, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 91, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13272, "duration_str": "132.72ms", "stmt_id": "view::services.medication_services.form.form:18", "connection": "ebdb"}, {"sql": "select * from `services` where `services`.`id` = '87' limit 1", "type": "query", "params": [], "bindings": ["87"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 18, "namespace": "view", "name": "services.medication_services.form.form", "line": 27}, {"index": 25, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 30, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 33, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 67, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 76, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 91, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16611, "duration_str": "166.11ms", "stmt_id": "view::services.medication_services.form.form:27", "connection": "ebdb"}, {"sql": "select count(*) as aggregate from `activity_actions` where `activity_id` = '464631' and `new_state_id` = '160' and `activity_actions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["464631", "160"], "hints": [], "backtrace": [{"index": 13, "namespace": null, "name": "/app/GisSort.php", "line": 315}, {"index": 14, "namespace": "view", "name": "services.medication_services.form.components.form-case-details", "line": 36}, {"index": 20, "namespace": "view", "name": "services.medication_services.form.form", "line": 48}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13852, "duration_str": "138.52ms", "stmt_id": "/app/GisSort.php:315", "connection": "ebdb"}, {"sql": "select * from `providers` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Medication.php", "line": 117}, {"index": 14, "namespace": "view", "name": "services.medication_services.form.components.form-supplier-details", "line": 11}, {"index": 20, "namespace": "view", "name": "services.medication_services.form.form", "line": 51}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13791, "duration_str": "137.91ms", "stmt_id": "/app/Medication.php:117", "connection": "ebdb"}, {"sql": "select * from `providers` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Medication.php", "line": 117}, {"index": 14, "namespace": "view", "name": "services.medication_services.form.components.form-supplier-details", "line": 18}, {"index": 20, "namespace": "view", "name": "services.medication_services.form.form", "line": 51}, {"index": 27, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 32, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 35, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 69, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 78, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 93, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1534, "duration_str": "153.4ms", "stmt_id": "/app/Medication.php:117", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 6}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13165000000000002, "duration_str": "131.65ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 8}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13035, "duration_str": "130.35ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 9}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.12975, "duration_str": "129.75ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 676}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1315, "duration_str": "131.5ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 438}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 687}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14465999999999998, "duration_str": "144.66ms", "stmt_id": "/app/User.php:438", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 446}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 694}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14345, "duration_str": "143.45ms", "stmt_id": "/app/User.php:446", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 442}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 700}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13134, "duration_str": "131.34ms", "stmt_id": "/app/User.php:442", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 552}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 707}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13118000000000002, "duration_str": "131.18ms", "stmt_id": "/app/User.php:552", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 434}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 719}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13097999999999999, "duration_str": "130.98ms", "stmt_id": "/app/User.php:434", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 450}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 739}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13263999999999998, "duration_str": "132.64ms", "stmt_id": "/app/User.php:450", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 454}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 745}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13428, "duration_str": "134.28ms", "stmt_id": "/app/User.php:454", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 458}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 751}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.132, "duration_str": "132ms", "stmt_id": "/app/User.php:458", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 462}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 757}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13777, "duration_str": "137.77ms", "stmt_id": "/app/User.php:462", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 522}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 765}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13371, "duration_str": "133.71ms", "stmt_id": "/app/User.php:522", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 526}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 772}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13244, "duration_str": "132.44ms", "stmt_id": "/app/User.php:526", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 466}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 785}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13544, "duration_str": "135.44ms", "stmt_id": "/app/User.php:466", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 474}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 791}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1351, "duration_str": "135.1ms", "stmt_id": "/app/User.php:474", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 478}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 797}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1323, "duration_str": "132.3ms", "stmt_id": "/app/User.php:478", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 482}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 803}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13290000000000002, "duration_str": "132.9ms", "stmt_id": "/app/User.php:482", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 486}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 809}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1363, "duration_str": "136.3ms", "stmt_id": "/app/User.php:486", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 490}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 819}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.15206999999999998, "duration_str": "152.07ms", "stmt_id": "/app/User.php:490", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 514}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 837}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13126, "duration_str": "131.26ms", "stmt_id": "/app/User.php:514", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 518}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 843}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13226, "duration_str": "132.26ms", "stmt_id": "/app/User.php:518", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 610}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 855}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13838999999999999, "duration_str": "138.39ms", "stmt_id": "/app/User.php:610", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 470}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 877}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13247, "duration_str": "132.47ms", "stmt_id": "/app/User.php:470", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 535}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 896}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13065000000000002, "duration_str": "130.65ms", "stmt_id": "/app/User.php:535", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 900}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13113, "duration_str": "131.13ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 905}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1349, "duration_str": "134.9ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 911}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14231, "duration_str": "142.31ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 917}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14409, "duration_str": "144.09ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 923}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13207, "duration_str": "132.07ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 928}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14296999999999999, "duration_str": "142.97ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 933}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14861000000000002, "duration_str": "148.61ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 938}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.1346, "duration_str": "134.6ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 943}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.31366000000000005, "duration_str": "313.66ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `areas` where `areas`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 16, "namespace": null, "name": "/app/User.php", "line": 585}, {"index": 17, "namespace": "view", "name": "layouts.main", "line": 948}, {"index": 23, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 30, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 35, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 38, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 72, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 75, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 81, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 96, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.14218, "duration_str": "142.18ms", "stmt_id": "/app/User.php:585", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1124}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13259, "duration_str": "132.59ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Client.php", "line": 34}, {"index": 15, "namespace": "view", "name": "layouts.main", "line": 1130}, {"index": 21, "namespace": "view", "name": "services.medication_services.form.form", "line": 602}, {"index": 28, "namespace": null, "name": "/vendor/symfony/http-foundation/Response.php", "line": 206}, {"index": 33, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 36, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 70, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 73, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 79, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 94, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13499, "duration_str": "134.99ms", "stmt_id": "/app/Client.php:34", "connection": "ebdb"}]}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"wilmer<PERSON>za10\"\n  \"user\" => array:50 [\n    \"id\" => 18\n    \"user_type\" => \"\"\n    \"email\" => \"<EMAIL>\"\n    \"affiliate_id\" => null\n    \"created_at\" => \"2024-09-17 10:51:15\"\n    \"updated_at\" => \"2025-09-15 13:28:34\"\n    \"full_name\" => \"WILMERR MAZA BANDA\"\n    \"first_name\" => \"WILMERR\"\n    \"last_name\" => \"MAZA BANDA\"\n    \"username\" => \"wilmermaza10\"\n    \"photo\" => \"user_photo/at8RH2yiiVxOjsYpnJozCU3S7UrqTy5O4T49x4i8.jpeg\"\n    \"area_id\" => 1\n    \"position_id\" => null\n    \"deleted_at\" => null\n    \"ascribed\" => 0\n    \"old_password1\" => \"$2y$10$AiImbU4Usf3eY8Hz865zbOnsfyZH2PbbPKJdibZUTAz/ywzOFXv.W\"\n    \"old_password2\" => \"$2y$10$cFzxSS3xlT4izkkAK7kze.tBRQ7Jm9N6uh1D4r8gCrLH39uAyhljq\"\n    \"old_password3\" => \"$2y$10$sLHd.4zpr6JC9myNAorkHOxRsO3Oys2y7SG0mbTvN/d5Bo3uYhUoS\"\n    \"last_login\" => \"2025-09-15 13:28:34\"\n    \"next_update\" => \"2030-03-06 03:00:00\"\n    \"extra_data\" => \"\"\n    \"zoom_api_key\" => null\n    \"zoom_api_secret\" => null\n    \"identification_number\" => \"**********\"\n    \"creator\" => null\n    \"has_schedule\" => \"0\"\n    \"company_id\" => null\n    \"department\" => null\n    \"municipality\" => null\n    \"ocupation\" => null\n    \"phone\" => \"**********\"\n    \"doc_type\" => \"CF\"\n    \"medical_record_number\" => null\n    \"license_number\" => null\n    \"rethus\" => null\n    \"signature\" => null\n    \"name_area\" => null\n    \"code_mnk\" => \"000010\"\n    \"brokerage_name\" => \"BCR CORREDORA DE SEGUROS SA\"\n    \"new_business_email\" => \"\"\n    \"advisor_name\" => \"000010\"\n    \"type_inter\" => \"G\"\n    \"correduria\" => null\n    \"code_correduria\" => \"009101\"\n    \"tipo_corredor\" => \"C\"\n    \"provider_id\" => null\n    \"resp_acsel_login\" => null\n    \"tomador_id\" => 0\n    \"unique_code\" => \"651827\"\n    \"autorizados\" => \"12486,42222\"\n  ]\n]", "api": "array:2 [\n  \"name\" => \"Guest\"\n  \"user\" => array:1 [\n    \"guest\" => true\n  ]\n]"}, "names": "web: wilmermaza10"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi", "_previous": "array:1 [\n  \"url\" => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "18", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"format": "html", "content_type": "text/html; charset=UTF-8", "status_text": "OK", "status_code": "200", "request_query": "[]", "request_request": "[]", "request_headers": "array:18 [\n  \"cookie\" => array:1 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IkdCNnJ6cDJNcVpDTnA1cFV4Y1RRb2c9PSIsInZhbHVlIjoibW9JTHZPc0syOGE4dGRHbVBCYWo5VEV0NXVaSkdIN0E2SFwvUVpvclJsM05zTWM2RUkyNjBTVCs5dFdoaFJLVisiLCJtYWMiOiI1MmM5YWQ3Yzg3NTk0MDg0MWUyMjNjMzU2NWQ4ZTNkZDc1Njc0Yzg2ZDU4MjY0YmI4MzA1MjJmYThiZjM2NDZlIn0%3D; laravel_session_cookie=eyJpdiI6Ik5INkZhSno2OWRiaGhaOWlqcnFsRGc9PSIsInZhbHVlIjoiQ0xDVzBicmM4N3J1dVJlWjc3MUVGOWs1SUEwZmpTZVZJd1h6ZExOb3lGQ2pONXpNS1FcL3ZqaW14bzNVXC92Q1RSQng2YU5HbWRCcjcwZE91RWdYNlh1ZHh5TXFybHNLZE1wOHNwc1pqVWFLOStVV0h2UXFHdDV2U1MzakNiODk1bSIsIm1hYyI6IjEzNWMzMTEwZTg1ODFiY2EzNTZmZDU1MTk4OWUwZTNjNGYzZWM2M2IyYmM4ZDAxNjQxYjdmZmI4Y2U2Y2ZlYjUifQ%3D%3D\"\n  ]\n  \"accept-language\" => array:1 [\n    0 => \"en,es-ES;q=0.9,es;q=0.8\"\n  ]\n  \"accept-encoding\" => array:1 [\n    0 => \"gzip, deflate, br, zstd\"\n  ]\n  \"sec-fetch-dest\" => array:1 [\n    0 => \"document\"\n  ]\n  \"sec-fetch-user\" => array:1 [\n    0 => \"?1\"\n  ]\n  \"sec-fetch-mode\" => array:1 [\n    0 => \"navigate\"\n  ]\n  \"sec-fetch-site\" => array:1 [\n    0 => \"none\"\n  ]\n  \"accept\" => array:1 [\n    0 => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  ]\n  \"user-agent\" => array:1 [\n    0 => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36\"\n  ]\n  \"upgrade-insecure-requests\" => array:1 [\n    0 => \"1\"\n  ]\n  \"sec-ch-ua-platform\" => array:1 [\n    0 => \"\"Windows\"\"\n  ]\n  \"sec-ch-ua-mobile\" => array:1 [\n    0 => \"?0\"\n  ]\n  \"sec-ch-ua\" => array:1 [\n    0 => \"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"\"\n  ]\n  \"cache-control\" => array:1 [\n    0 => \"max-age=0\"\n  ]\n  \"connection\" => array:1 [\n    0 => \"keep-alive\"\n  ]\n  \"host\" => array:1 [\n    0 => \"oceanica-qa.renapp.com\"\n  ]\n  \"content-length\" => array:1 [\n    0 => \"\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"\"\n  ]\n]", "request_server": "array:62 [\n  \"PHP_EXTRA_CONFIGURE_ARGS\" => \"--enable-fpm --with-fpm-user=www-data --with-fpm-group=www-data --disable-cgi\"\n  \"LANGUAGE\" => \"es_ES.UTF-8\"\n  \"HOSTNAME\" => \"b44486792d1f\"\n  \"PHP_INI_DIR\" => \"/usr/local/etc/php\"\n  \"HOME\" => \"/var/www\"\n  \"PHP_LDFLAGS\" => \"-Wl,-O1 -pie\"\n  \"PHP_CFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_VERSION\" => \"7.2.34\"\n  \"GPG_KEYS\" => \"1729F83938DA44E27BA0F4D3DBDB397470D12172 B1B44D8F021E4E2D6021E995DC9FF8D3EE5AF27F\"\n  \"PHP_CPPFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_ASC_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz.asc\"\n  \"PHP_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz\"\n  \"PATH\" => \"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"\n  \"LANG\" => \"es_ES.UTF-8\"\n  \"PHPIZE_DEPS\" => \"autoconf \\t\\tdpkg-dev \\t\\tfile \\t\\tg++ \\t\\tgcc \\t\\tlibc-dev \\t\\tmake \\t\\tpkg-config \\t\\tre2c\"\n  \"LC_ALL\" => \"es_ES.UTF-8\"\n  \"PWD\" => \"/var/www/html\"\n  \"PHP_SHA256\" => \"409e11bc6a2c18707dfc44bc61c820ddfd81e17481470f3405ee7822d8379903\"\n  \"USER\" => \"www-data\"\n  \"HTTP_COOKIE\" => \"XSRF-TOKEN=eyJpdiI6IkdCNnJ6cDJNcVpDTnA1cFV4Y1RRb2c9PSIsInZhbHVlIjoibW9JTHZPc0syOGE4dGRHbVBCYWo5VEV0NXVaSkdIN0E2SFwvUVpvclJsM05zTWM2RUkyNjBTVCs5dFdoaFJLVisiLCJtYWMiOiI1MmM5YWQ3Yzg3NTk0MDg0MWUyMjNjMzU2NWQ4ZTNkZDc1Njc0Yzg2ZDU4MjY0YmI4MzA1MjJmYThiZjM2NDZlIn0%3D; laravel_session_cookie=eyJpdiI6Ik5INkZhSno2OWRiaGhaOWlqcnFsRGc9PSIsInZhbHVlIjoiQ0xDVzBicmM4N3J1dVJlWjc3MUVGOWs1SUEwZmpTZVZJd1h6ZExOb3lGQ2pONXpNS1FcL3ZqaW14bzNVXC92Q1RSQng2YU5HbWRCcjcwZE91RWdYNlh1ZHh5TXFybHNLZE1wOHNwc1pqVWFLOStVV0h2UXFHdDV2U1MzakNiODk1bSIsIm1hYyI6IjEzNWMzMTEwZTg1ODFiY2EzNTZmZDU1MTk4OWUwZTNjNGYzZWM2M2IyYmM4ZDAxNjQxYjdmZmI4Y2U2Y2ZlYjUifQ%3D%3D\"\n  \"HTTP_ACCEPT_LANGUAGE\" => \"en,es-ES;q=0.9,es;q=0.8\"\n  \"HTTP_ACCEPT_ENCODING\" => \"gzip, deflate, br, zstd\"\n  \"HTTP_SEC_FETCH_DEST\" => \"document\"\n  \"HTTP_SEC_FETCH_USER\" => \"?1\"\n  \"HTTP_SEC_FETCH_MODE\" => \"navigate\"\n  \"HTTP_SEC_FETCH_SITE\" => \"none\"\n  \"HTTP_ACCEPT\" => \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"\n  \"HTTP_USER_AGENT\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36\"\n  \"HTTP_UPGRADE_INSECURE_REQUESTS\" => \"1\"\n  \"HTTP_SEC_CH_UA_PLATFORM\" => \"\"Windows\"\"\n  \"HTTP_SEC_CH_UA_MOBILE\" => \"?0\"\n  \"HTTP_SEC_CH_UA\" => \"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"\"\n  \"HTTP_CACHE_CONTROL\" => \"max-age=0\"\n  \"HTTP_CONNECTION\" => \"keep-alive\"\n  \"HTTP_HOST\" => \"oceanica-qa.renapp.com\"\n  \"PATH_INFO\" => \"\"\n  \"SCRIPT_FILENAME\" => \"/var/www/html/public/index.php\"\n  \"REDIRECT_STATUS\" => \"200\"\n  \"SERVER_NAME\" => \"oceanica.renapp.us\"\n  \"SERVER_PORT\" => \"443\"\n  \"SERVER_ADDR\" => \"**********\"\n  \"REMOTE_PORT\" => \"36022\"\n  \"REMOTE_ADDR\" => \"**********\"\n  \"SERVER_SOFTWARE\" => \"nginx/1.26.3\"\n  \"GATEWAY_INTERFACE\" => \"CGI/1.1\"\n  \"HTTPS\" => \"on\"\n  \"REQUEST_SCHEME\" => \"https\"\n  \"SERVER_PROTOCOL\" => \"HTTP/1.1\"\n  \"DOCUMENT_ROOT\" => \"/var/www/html/public\"\n  \"DOCUMENT_URI\" => \"/index.php\"\n  \"REQUEST_URI\" => \"/servicio/518948/medication_services\"\n  \"SCRIPT_NAME\" => \"/index.php\"\n  \"CONTENT_LENGTH\" => \"\"\n  \"CONTENT_TYPE\" => \"\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"QUERY_STRING\" => \"\"\n  \"FCGI_ROLE\" => \"RESPONDER\"\n  \"PHP_SELF\" => \"/index.php\"\n  \"REQUEST_TIME_FLOAT\" => 1758086468.4404\n  \"REQUEST_TIME\" => 1758086468\n  \"argv\" => []\n  \"argc\" => 0\n]", "request_cookies": "array:2 [\n  \"XSRF-TOKEN\" => null\n  \"laravel_session_cookie\" => \"nuE9DDo4EAWK7Fki1KZCUv5MCeA3itw4PrAmTYsH\"\n]", "response_headers": "array:5 [\n  \"cache-control\" => array:1 [\n    0 => \"no-cache, private\"\n  ]\n  \"date\" => array:1 [\n    0 => \"Wed, 17 Sep 2025 05:21:32 GMT\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"text/html; charset=UTF-8\"\n  ]\n  \"set-cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IjhVXC9zaVQ1cFV2clVVSzNPcFR0bFBRPT0iLCJ2YWx1ZSI6IkR5RWlNQmxQdDJcLzM3OVUyOHZEQjFTOVJcL2RGd1wvTjB1MmM3dllFM3krdWIwT0JTZEpEbFlzeldlRjdpMlV1d1oiLCJtYWMiOiJhNDY3MzdjNGVlYjY3MDEzMzBlNjNiZmVjMmZlYThjYjE1NGNiNTViMGM5NzhkZmM0ZDA3YjI1NjE0MjUyNmM1In0%3D; expires=Thu, 18-Sep-2025 01:21:41 GMT; Max-Age=72000; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6Ikc2SHFXckVkYTZzY2I5cGFkMGlzZHc9PSIsInZhbHVlIjoiRituTTY0d3paWjk4Y3BNaE1ZUEFnWklRdjhyYmFMWE1ic2F5eWZ5bTJaRWVMbGJ4U20xTVNsY2taaXVBNDMxb0hBYVNkclk0dG5tVFlpNHl6amdiKzh2aDBiWFlYeGVmTUNoUDBkXC9iYlZJMWJmcm0ydGdBK203aUxaOGU0OXJSIiwibWFjIjoiM2E1NTM3YmZkYmRjYjhiMTdlM2I1MjkyNDk0YmIxMzc3ZTI0NDgxN2VjMjE2OTc5ZmE5YmNhZDhmNzk0Y2MxZSJ9; expires=Thu, 18-Sep-2025 01:21:41 GMT; Max-Age=72000; path=/; httponly\"\n  ]\n  \"Set-Cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6IjhVXC9zaVQ1cFV2clVVSzNPcFR0bFBRPT0iLCJ2YWx1ZSI6IkR5RWlNQmxQdDJcLzM3OVUyOHZEQjFTOVJcL2RGd1wvTjB1MmM3dllFM3krdWIwT0JTZEpEbFlzeldlRjdpMlV1d1oiLCJtYWMiOiJhNDY3MzdjNGVlYjY3MDEzMzBlNjNiZmVjMmZlYThjYjE1NGNiNTViMGM5NzhkZmM0ZDA3YjI1NjE0MjUyNmM1In0%3D; expires=Thu, 18-Sep-2025 01:21:41 GMT; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6Ikc2SHFXckVkYTZzY2I5cGFkMGlzZHc9PSIsInZhbHVlIjoiRituTTY0d3paWjk4Y3BNaE1ZUEFnWklRdjhyYmFMWE1ic2F5eWZ5bTJaRWVMbGJ4U20xTVNsY2taaXVBNDMxb0hBYVNkclk0dG5tVFlpNHl6amdiKzh2aDBiWFlYeGVmTUNoUDBkXC9iYlZJMWJmcm0ydGdBK203aUxaOGU0OXJSIiwibWFjIjoiM2E1NTM3YmZkYmRjYjhiMTdlM2I1MjkyNDk0YmIxMzc3ZTI0NDgxN2VjMjE2OTc5ZmE5YmNhZDhmNzk0Y2MxZSJ9; expires=Thu, 18-Sep-2025 01:21:41 GMT; path=/; httponly\"\n  ]\n]", "path_info": "/servicio/518948/medication_services", "session_attributes": "array:5 [\n  \"_token\" => \"aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi\"\n  \"_previous\" => array:1 [\n    \"url\" => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n  ]\n  \"_flash\" => array:2 [\n    \"old\" => []\n    \"new\" => []\n  ]\n  \"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d\" => 18\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n]"}}