<?php $__env->startSection('title', 'FORMULARIO MEDICAMENTOS - SORT'); ?>

<?php $__env->startSection('menu'); ?>
    ##parent-placeholder-252a25667dc7c65fe0e9bf62d474bbab9bab4068##
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="ui basic segment">
        <h1 class="ui header">
            Formulario medicamentos - SORT
            <div class="sub header">Campos con <span style="color: red;" class="required">*</span> obligatorios.</div>
        </h1>

        <div class="ui secondary segment">
            <div class="ui grid">
                <div class="four column row">
                    <div class="column left aligned">
                        <b>Identificación:</b> <?php echo e($activity->affiliate->doc_type); ?> <?php echo e($activity->affiliate->doc_number); ?>

                    </div>
                    <div class="column center aligned">
                        <b>Nombre:</b> <a
                            href="<?php echo e(secure_url('afiliado/' . $activity->affiliate_id)); ?>"><?php echo e(ucwords(strtolower($activity->affiliate->full_name))); ?></a>
                    </div>
                    <div class="column right aligned">
                        <b>Actividad:</b> <a
                            href="<?php echo e(secure_url('servicio/' . $activity->id)); ?>"><?php echo e($activity->service->name); ?></a>
                    </div>
                    <?php if($medication_service->factura_id_dokkar): ?>
                        <div class="column right aligned">
                            <b>Estado de orden medicamentos:</b> <span id="medication_order_state"></span>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>

        <form id="form-medication-services" class="ui attached form"
            action="<?php echo e(secure_url('/servicio/' . $id . '/medication_services/save')); ?>" method="post" autocomplete="off">
            <?php echo e(csrf_field()); ?>

            <div class="ui styled fluid accordion">
                <!-- Formulario para datos del paciente -->
                <?php echo $__env->make('services.medication_services.form.components.form-insured-details', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <!-- Formulario datos del caso -->
                <?php echo $__env->make('services.medication_services.form.components.form-case-details', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <!-- Formulario para DATOS DEL PROVEEDOR (IPS)-->
                <?php echo $__env->make('services.medication_services.form.components.form-supplier-details', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <!-- Formulario para DIAGNOSTICOS -->
                <?php echo $__env->make('services.medication_services.form.components.form-diagnosis', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

                <?php if($medical_prescriptions && $medical_prescriptions->isNotEmpty()): ?>
                    <!-- Formulario para FÓRMULA MÉDICA -->
                    <?php echo $__env->make('services.medication_services.form.components.form-medical-prescription', [
                        'medical_prescriptions' => $medical_prescriptions,
                    ], array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                <?php endif; ?>

                <?php if($controlled_medications && $controlled_medications->isNotEmpty()): ?>
                    <!-- Formulario para FÓRMULA DE MEDICAMENTOS CONTROLADOS -->
                    <?php echo $__env->make(
                        'services.medication_services.form.components.form-controlled-medication-formula',
                        ['controlled_medications' => $controlled_medications]
                    , array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                <?php endif; ?>

                <!-- SELECCIÓN DE FARMACIA -->
                <?php if(
                    $activity->state_id != \App\States\StateMedicationServiceSort::REGISTRADO &&
                        $activity->state_id !=
                            \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA &&
                        $activity->state_id != \App\States\StateMedicationServiceSort::SERVICIO_ANULADO): ?>
                    <?php echo $__env->make('services.medication_services.form.components.pharmacies', [
                        'medical_prescriptions' => $medical_prescriptions,
                        'controlled_medications' => $controlled_medications,
                    ], array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                <?php endif; ?>

                <?php if($activity->state_id != \App\States\StateMedicationServiceSort::ORDEN_DE_ATENCION_COMPRA_INTERNA_GENERADA): ?>

                    <!-- subpestaña Información de entrega -->
                    <?php if(
                        $activity->state_id != \App\States\StateMedicationServiceSort::REGISTRADO &&
                            $activity->state_id !=
                                \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::SERVICIO_ANULADO &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_ASIGNADA_A_PROVEEDOR &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
                        <?php echo $__env->make('services.medication_services.form.components.form-delivery-info', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                    <?php endif; ?>

                    <!-- subpestaña datos generales -->
                    <?php if(
                        $activity->state_id != \App\States\StateMedicationServiceSort::REGISTRADO &&
                            $activity->state_id !=
                                \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_EN_REVISION_POR_AUDITORIA_MEDICA &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::SERVICIO_ANULADO &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::SOLICITUD_MEDICAMENTOS_ASIGNADA_A_PROVEEDOR &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA &&
                            $activity->state_id != \App\States\StateMedicationServiceSort::FARMACIA_SELECCIONADA): ?>
                        <?php echo $__env->make('services.medication_services.form.components.form-general-info', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            <div style="margin-top: 25px;">
                <div class="ui error message"></div>
                <div class="fields">
                    <?php if($activity->state_id != \App\States\StateMedicationServiceSort::ORDEN_DE_ATENCION_COMPRA_INTERNA_GENERADA): ?>
                        <div class="four wide field">
                            <button class="ui primary fluid button disable_save_button" type="submit" id="saveButton"><i
                                    class="save icon"></i>
                                <?php switch($activity->state_id ?? 0):
                                    case (\App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
                                        Guardar farmacia
                                    <?php break; ?>

                                    <?php case (\App\States\StateMedicationServiceSort::FARMACIA_SELECCIONADA): ?>
                                        Guardar tipo de entrega
                                    <?php break; ?>

                                    <?php case (\App\States\StateMedicationServiceSort::TIPO_DE_ENTREGA_SELECCIONADA): ?>
                                        Enviar orden de prefacutra
                                    <?php break; ?>

                                    <?php default: ?>
                                        Guardar datos
                                <?php endswitch; ?>
                            </button>
                        </div>
                    <?php endif; ?>
                    <div class="six wide field">
                        <a href="<?php echo e(secure_url('/servicio/' . $activity->id)); ?>">
                            <button class="ui button secondary" type="button"><i class="arrow left icon"></i>Volver a la
                                actividad
                            </button>
                        </a>
                    </div>
                </div>
            </div>

        </form>
    </div>


    <style>
        .ui.grid .column {
            padding: 0.5rem 1rem !important;
        }

        .readonly {
            background: rgba(0, 0, 0, .05) !important;
        }

        .textarea-expand {
            background: rgba(0, 0, 0, 0.05) !important;
        }
    </style>

    <script src="<?php echo e(secure_url('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/locale/es.min.js')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(secure_url('js/dataVademecumReady.js?v=1.4.15')); ?>"></script>
    <script type="text/javascript" src="<?php echo e(secure_url('js/policy_issuance_data_group.js?v=1.4.15')); ?>"></script>

    <!--inicializar los dropdowns y accordion-->
    <script type="text/javascript">
        $(document).ready(function() {
            $('.ui.dropdown').dropdown();
            $('.ui.accordion').accordion();

        });
    </script>

    <!-- Textarea expansion, esto para ajustar el tamaño de la textarea para aquellas notas médicas guardadas -->
    <script>
        $(document).ready(function() {
            // Ajusta la altura al cargar la página
            $(".textarea-expand").each(function() {
                var altura = $(this)[0].scrollHeight;
                $(this).height(altura);
            });
        });
    </script>
    <!--Preparar lista de vademecum-->
    <script>
        // Solicitud AJAX para obtener los datos del vademécum desde el backend
        $.ajax({
            url: "<?php echo e(secure_url('/obtenerLista/vademecum')); ?>", // URL
            method: "GET",
            success: function(response) {
                if (response && response.List) {
                    vademecum = response.List; // Almacena los datos obtenidos

                    // Inicializa los dropdowns y campos para todos los grupos
                    initializeDropdownsAndFields();
                } else {
                    console.error("No se recibieron datos válidos del servidor");
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al cargar datos del vademécum desde la base de datos:", error);
            }
        });
    </script>

    <!-- Script para los cie10 necesarios en los formularios -->
    <script>
        $.getJSON('/js/cie10.json', function(json) {
            cie10 = json.map(item => ({
                COD: item.COD,
                DESCRIPTION: capitalizeFirstLetter(item.DESCRIPTION)
            }));
            $('form .ui.search.diagnostic.code').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function(result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
        });
    </script>

    <!-- Script del Formulario de  DIAGNOSTICOS -->
    <script>
        let addDiagnostic = function(e) {

            let $fields = $('#diagnostic_model').clone(true);
            $fields.removeAttr('id');
            $fields.find('a').click(function() {
                $(this).parent().parent().remove();
            });
            $fields.find('.ui.dropdown').dropdown({
                forceSelection: false
            });
            $fields.find('.ui.search.diagnostic input').change(function() {
                let valid = false;

                $(this).parents('.fields').find('input.description').val('');

                for (let i = 0; i < cie10.length; i++) {
                    if (cie10[i].COD == $(this).val().toUpperCase()) {
                        $(this).parents('.fields').find('input.description').val(cie10[i].DESCRIPTION);
                        valid = true;
                    }
                }

                if (!valid && $(this).val() != '') {
                    $(this).val('');
                }
            });
            $fields.find('.ui.search.diagnostic').search({
                source: cie10,
                fields: {
                    title: 'COD',
                    description: 'DESCRIPTION'
                },
                searchFields: ['COD', 'DESCRIPTION'],
                regExp: {
                    escape: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
                    beginsWith: ''
                },
                showNoResults: true,
                maxResults: 250,
                searchFullText: false,
                error: {
                    noResults: 'No se encontraron resultados para tu búsqueda.'
                },
                onSelect: function(result, response) {
                    $(this).parents('.fields').find('input.description').val(result.DESCRIPTION);
                }
            });
            $('#diagnostics').append($fields);
            $fields.show();
            return false;
        };
    </script>

    <!-- Validaciones del formulario-->
    <script>
        document.getElementById('form-medication-services').addEventListener('submit', function(event) {
            // Prevent form submission temporarily
            event.preventDefault();

            // Si el formulario no es válido, no enviarlo
            if (!validateRequiredFields($(this).attr('id'))) {
                return false;
            } else {
                this.submit();
            }
        });

        //Validaciones del formulario campos requeridos
        function validateRequiredFields(formId) {
            //colocar loading pantalla
            loadingMain(true);
            //deshabilitar el botón de guardardo del formulario
            $('.disable_save_button').prop('disabled', true);

            const invalidFieldsBySection = {};
            const form = document.getElementById(formId);
            if (!form) {
                console.error('Form not found');
                loadingMain(false);
                return false; // Return false if the form is not found
            }

            const titles = form.querySelectorAll('.title');
            titles.forEach(title => {
                const sectionTitle = title.textContent.trim();

                // Omitir la validación si es la sección "DATOS DEL CASO"
                if (sectionTitle === "Datos del caso") return;

                invalidFieldsBySection[sectionTitle] = [];
                const content = title.nextElementSibling; // Obtener el siguiente div con el contenido

                // Validar campos requeridos generales
                const requiredFields = content.querySelectorAll(
                    '.field.required textarea, .field.required input[type="text"], .field.required input[type="hidden"]'
                );

                requiredFields.forEach(field => {
                    // Ignorar el campo de lateralidad
                    if (field.name === "diagnostics[laterality][]") return;

                    // Validar inputs de tipo datepicker o timepicker, solo el input oculto
                    if (field.classList.contains('datepicker') || field.classList.contains('timepicker')) {
                        const hiddenInput = content.querySelector(
                            `input[type="hidden"][name="${field.name}_submit"]`);
                        if (hiddenInput && !hiddenInput.value) {
                            const label = field.closest('.field').querySelector('label');
                            if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent
                                    .trim())) {
                                invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                            }
                        }
                        return; // Salir de la iteración para evitar validar el input visible
                    }

                    // Validar textarea y input de texto, pero no el oculto
                    if ((field.tagName === 'TEXTAREA' && field.value.trim() === '') ||
                        (field.tagName === 'INPUT' && field.type !== 'hidden' && field.value.trim() === '')
                    ) {
                        const label = field.closest('.field').querySelector('label');
                        if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent
                                .trim())) {
                            invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                        }
                    }

                    // Validar input oculto (como en el canal de consulta)
                    if (field.type === 'hidden' && !field.value) {
                        const label = field.closest('.field').querySelector('label');
                        if (label && !invalidFieldsBySection[sectionTitle].includes(label.textContent
                                .trim())) {
                            invalidFieldsBySection[sectionTitle].push(label.textContent.trim());
                        }
                    }
                });

            });

            // Crear el mensaje de alerta utilizando el formato requerido
            let message = Object.entries(invalidFieldsBySection)
                .filter(([, fields]) => fields.length > 0)
                .map(([accordion, fields]) => `<strong>${accordion}:</strong><br>- ${fields.join('<br>- ')}`)
                .join('<br><br>');


            let messageCotrol = '<strong>Control de entrega medicamentos </strong>';
            let messageCotrolVal = false;

            if (document.getElementById("pickup_name")) {

                const nameInput = document.getElementById("pickup_name");

                if (nameInput.value) {
                    if (nameInput.value.length < 2) {
                        messageCotrol += "<br>-El nombre debe tener al menos 2 caracteres.";
                        messageCotrolVal = true;
                    }
                }
            }

            if (document.getElementById("pickup_phone")) {
                const phoneInput = document.getElementById("pickup_phone");
                if (phoneInput.value) {
                    if (phoneInput.value.length < 7 || phoneInput.value.length > 10) {
                        messageCotrol += "<br>-El número de teléfono debe tener entre 7 y 10 dígitos.";
                        messageCotrolVal = true;
                    }
                }
            }

            if (message) {
                Swal.fire({
                    title: 'Campos requeridos',
                    html: `<div style="max-height: 300px; overflow-y: auto;">Por favor, completa correctamente los siguientes campos:<br><br>${message}</div>`,
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Volver a activar los botones si la validación falla
                        $('.disable_save_button').prop('disabled', false);
                        loadingMain(false);
                    }
                });
                return false; // Si hay campos inválidos, no se debe enviar el formulario
            }

            if (messageCotrolVal) {
                Swal.fire({
                    title: 'Campos requeridos',
                    html: `<div style="max-height: 300px; overflow-y: auto;">Por favor, completa correctamente los siguientes campos:<br><br>${messageCotrol}</div>`,
                    icon: 'warning',
                    confirmButtonText: 'Aceptar',
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Volver a activar los botones si la validación falla
                        $('.disable_save_button').prop('disabled', false);
                        loadingMain(false);
                    }
                });
                return false;
            }

            <?php if($activity->state_id == \App\States\StateMedicationServiceSort::FARMACIA_SELECCIONADA): ?>
                let lat = parseFloat($('input[name="delivery_latitude"]').val());
                let lon = parseFloat($('input[name="delivery_longitude"]').val());
                let method = $('input[name="delivery_method"]').val();

                if (method == 'EXPRESS') {
                    if (isNaN(lat) || lat < -90 || lat > 90) {
                        Swal.fire({
                            title: 'Latitud inválida',
                            text: 'La latitud debe estar entre -90 y 90.',
                            icon: 'warning',
                            confirmButtonText: 'Aceptar',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Volver a activar los botones si la validación falla
                                $('.disable_save_button').prop('disabled', false);
                                loadingMain(false);
                            }
                        });
                        return false;
                    }

                    if (isNaN(lon) || lon < -180 || lon > 180) {
                        loadingMain(false); // ocultar el loader
                        //mostrar un mensaje swal.fire
                        Swal.fire({
                            title: 'Longitud inválida',
                            text: 'La longitud debe estar entre -180 y 180.',
                            icon: 'warning',
                            confirmButtonText: 'Aceptar',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Volver a activar los botones si la validación falla
                                $('.disable_save_button').prop('disabled', false);
                                loadingMain(false);
                            }
                        });
                        return false;
                    }
                }
            <?php endif; ?>

            return true; // Si todos los campos son válidos
        }


        // Definir la función para limpiar los campos provincia, cantón distrito según el acordeon que clickeo
        let clearLocationFields = function(button) {

            //encontrar el div que contiene todo los inputs
            let content = $(button).closest('.content');

            // Limpiar todos los campos tipo dropdown dentro del div content
            content.find('.ui.dropdown').each(function() {
                $(this).dropdown('clear');
            });

            // Limpiar todos los campos tipo input[text] dentro del div content
            content.find('input[type="text"]').each(function() {
                $(this).val(''); // Limpiar el valor del input
            });

            // Limpiar todos los campos tipo input[number] dentro del div content
            content.find('input[type="number"]').each(function() {
                $(this).val(''); // Limpiar el valor del input
            });

            // Limpiar todos los campos tipo textarea dentro del div content
            content.find('textarea').each(function() {
                $(this).val(''); // Limpiar el contenido del textarea
            });

            // Limpiar todos los campos tipo datepicker dentro del div content
            content.find('.datepicker').each(function() {
                $(this).pickadate('picker').clear(); // Limpiar el valor del datepicker
            });

            // Limpiar todos los campos tipo input[hidden] dentro del div content
            content.find('input[type="hidden"]').each(function() {
                $(this).val(''); // Limpiar el valor del input hidden
            });

        };
    </script>

    <script>
        $('.medication-input-to-lower-case').each(function() {
            let value = $(this).val().toLowerCase();
            $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
        });
    </script>

    <script>
        // Evento para transformar el texto del input en tiempo real
        $('.medication-input-search-to-lower-case').on('input', function() {
            let value = $(this).val().toLowerCase();
            // Capitaliza la primera letra y el resto en minúsculas
            $(this).val(value.charAt(0).toUpperCase() + value.slice(1));
        });
    </script>

    <!-- coloca los textarea auto resize -->
    <script>
        $(document).ready(function() {
            $('.auto-resize').each(function() {
                // Fija el height inicial en 37px solo desde JavaScript
                $(this).css('height', '38.21px');

                // Ajuste automático del height al escribir
                $(this).on('input', function() {
                    this.style.height = '38.21px'; // Reinicia a 37px antes de ajustar
                    this.style.height = (this.scrollHeight) + 'px'; // Ajusta según el contenido
                });
            });

        });
    </script>

    <script>
        // Función para capitalizar solo la primera letra de la oración
        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
        }
    </script>

    <!--consultar Estado de la orden de medicamentos  -->
    <script>
        $(document).ready(function() {
            // Realizar una solicitud AJAX para obtener el estado de la orden de medicamentos
            <?php if($activity->factura_id_dokkar): ?>
                $.ajax({
                    url: "<?php echo e(secure_url('/servicio/' . $activity->id . '/orden_status')); ?>", // URL para obtener el estado
                    method: "GET",
                    success: function(response) {
                        const {
                            data: {
                                status
                            }
                        } = response;

                        if (status) {
                            // Actualizar el contenido del span con el estado obtenido
                            $('#medication_order_state').text(status);
                        } else {
                            console.error("No se recibió un estado válido del servidor");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error al cargar el estado de la orden de medicamentos:", error);
                    }
                });
            <?php endif; ?>
        });
    </script>
    <?php echo $__env->yieldPushContent('scripts_medication_services'); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>