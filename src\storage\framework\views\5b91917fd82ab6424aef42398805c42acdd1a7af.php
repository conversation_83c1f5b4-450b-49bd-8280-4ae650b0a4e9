<div class="title"><i class="dropdown icon"></i>
    Farmacia <?php if($activity->state_id == \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
        <span style="color: red;" class="required">*</span>
    <?php endif; ?>
</div>
<div class="content">

    <div class="location-group" data-group-id="6">
        <div class="ui secondary segment">
            <div class="field">
                <div class="three fields">
                    <div class="field">
                        <label>Provincia</label>
                        <div
                            class="province ui search selection dropdown <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                            <input type="hidden" id="province_medical_prescription"
                                name="province_medical_prescription" class="minus_font province_value"
                                value="<?php echo e($medication_service->province_medical_prescription ?? ($medication_service->province_controlled_medication ?? '')); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Cantón</label>
                        <div
                            class="canton ui search selection dropdown <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                            <input type="hidden" id="canton_medical_prescription" name="canton_medical_prescription"
                                class="minus_font canton_value"
                                value="<?php echo e($medication_service->canton_medical_prescription ?? ($medication_service->canton_controlled_medication ?? '')); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Distrito</label>
                        <div
                            class="district ui search selection dropdown  <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                            <input type="hidden" id="district_medical_prescription"
                                name="district_medical_prescription" class="minus_font district_value"
                                value="<?php echo e($medication_service->district_medical_prescription ?? ($medication_service->district_controlled_medication ?? '')); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>

    <div class="ui divider"></div>

    <div class="three fields">
        
        <div class="field <?php if($activity->state_id == \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> required <?php endif; ?>">
            <label for="follow">Requiere compra externa </label>
            <div id="externalPurchaseDropdown"
                class="ui selection dropdown <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                <input type="hidden" name="external_purchase"
                    value="<?php echo e(old('external_purchase', $medication_service->external_purchase ?? '')); ?>">
                <i class="dropdown icon"></i>
                <div class="default text">Selecciona uno</div>
                <div class="menu">
                    <div class="item" data-value="1"
                        <?php echo e(old('external_purchase', $medication_service->external_purchase ?? '') == 1 ? 'selected' : ''); ?>>
                        Sí
                    </div>
                    <div class="item" data-value="0"
                        <?php echo e(old('external_purchase', $medication_service->external_purchase ?? '') == 0 ? 'selected' : ''); ?>>
                        No
                    </div>
                </div>
            </div>
        </div>

        <?php if($activity->state_id == \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
            <div class="field" id="searchPharmacyField" style="display:none;">
                <button class="ui secondary button fluid" type="button" id="btnSearchPharmacy"
                    style="margin-top:1.8em;">
                    <i class="search icon"></i> Buscar farmacias cercanas
                </button>
            </div>

            <div class="field" id="updatePharmacyField" style="display:none;">
                <button class="ui primary button fluid" type="button" id="btnEditMeds" style="margin-top:1.8em;">
                    <i class="edit icon"></i> Actualizar medicamentos
                </button>
            </div>

            <div class="field" id="savePharmacyField" style="display:none;">
                <button class="ui primary button fluid" type="button" id="btnSaveMeds" style="margin-top:1.8em;">
                    <i class="save icon"></i> Guardar medicamentos
                </button>
            </div>

            <div class="field" id="cancelarMedicamentosField" style="display:none;">
                <button class="ui red button fluid" type="button" id="btnCancelMeds" style="margin-top:1.8em;">
                    <i class="undo icon"></i> Cancelar
                </button>
            </div>
        <?php endif; ?>


        <!-- Campos ocultos para la farmacia -->
        <input type="hidden" name="pharmacy_id" id="pharmacy_id" value=""
            <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>>
        <input type="hidden" name="pharmacy_name" id="pharmacy_name" value=""
            <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>>
        <input type="hidden" name="pharmacy_chain" id="pharmacy_chain" value=""
            <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>>

    </div>

    <div class="field">
        <div id="bodyPharmacies" style="display:none;">
            <div class="infoPharmacies">
                <div class="field">
                    <table class="ui celled table transition" style="margin-top: 1em;">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Código</th>
                                <th>Molécula</th>
                                <th>Tipo</th>
                                <th>Descripción</th>
                                <th>Cantidad</th>
                                <th>Estado</th>
                            </tr>
                        </thead>
                        <tbody id="pharmaciesTable" style="display: none;">
                            <?php if($medications && count($medications) > 0): ?>
                                
                                <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $medicine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td><?php echo e($medicine->codigo ?? ''); ?></td>
                                        <td><?php echo e($medicine->molecula ?? ''); ?></td>
                                        <td><?php echo e($medicine->tipo ?? ''); ?></td>
                                        <td><?php echo e($medicine->descrip ?? ''); ?></td>
                                        <td><?php echo e($medicine->quantity_numbers ?? ''); ?></td>
                                        <td class="medicationState"></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>

                        </tbody>
                    </table>
                </div>
            </div>

            <div class="infoMedications" style="display:none;">
                <div class="field">
                    <div class="accordion transition">
                        <?php if($medical_prescriptions && $medical_prescriptions->isNotEmpty()): ?>
                            <!-- Formulario para FÓRMULA MÉDICA -->
                            <?php echo $__env->make(
                                'services.medication_services.form.components.form-medical-prescription-edit',
                                ['medical_prescriptions' => $medical_prescriptions]
                            , array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                        <?php endif; ?>

                        <?php if($controlled_medications && $controlled_medications->isNotEmpty()): ?>
                            <!-- Formulario para FÓRMULA DE MEDICAMENTOS CONTROLADOS -->
                            <?php echo $__env->make(
                                'services.medication_services.form.components.form-medical-prescription-edit',
                                ['medical_prescriptions' => $controlled_medications]
                            , array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="three fields">
        <div class=" field" id="pharmacyField" style="display: none">
            <label>Seleccionar farmacia</label>
            <div id="pharmacyDropdown"
                class="ui selection search dropdown <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                <i class="dropdown icon"></i>
                <div class="default text" <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> style="color: black;" <?php endif; ?>>
                    <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
                        <?php echo e($medication_service->sucursal_name ?? ''); ?>

                    <?php else: ?>
                        Seleccionar
                    <?php endif; ?>
                </div>
                <div class="menu"></div>
            </div>
        </div>


    </div>
</div>

<?php $__env->startPush('scripts_medication_services'); ?>
    <!--Compra externa-->
    <script>
        $(document).ready(function() {
            $('#externalPurchaseDropdown').dropdown({
                onChange: function(value) {
                    if (value == "0") { // Si selecciona "No"
                        $('#bodyPharmacies').show();
                        $('#pharmaciesTable').show() //ocultar tbody de la tabla
                        $('#pharmacyField').show().addClass('required');
                        $('#searchPharmacyField').show();
                        $('#updatePharmacyField').show();
                        // Resetear el dropdown de farmacia
                        $('#pharmacyDropdown').dropdown('clear');
                        // $('#pharmacyDropdown .menu').empty(); // Eliminar elementos agregados dinámicamente
                        $('#pharmacyDropdown .default.text').text(
                            'Seleccionar'); // Restaurar el texto por defecto

                    } else { // Si selecciona "Si"
                        $('#bodyPharmacies').hide();
                        $('#pharmacyField').hide().removeClass('required');
                        $('#searchPharmacyField').hide();
                        $('#updatePharmacyField').hide();
                    }
                }
            });

            <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?>
                // Verificar el valor al cargar la página
                let initialValue = <?php echo e($medication_service->external_purchase); ?>;
                if (initialValue == "0") {
                    $('#bodyPharmacies').show();
                    $('#pharmaciesTable').show();
                    $('.medicationState').text('Disponible');
                    $('#pharmacyField').show();
                    $('#searchPharmacyField').show();
                }
            <?php endif; ?>

            $('#pharmacyDropdown').dropdown({
                onChange: function(value, text, $selectedItem) {
                    // Sobrescribe el valor del input manualmente
                    $('input[name="pharmacy"]').val(value);
                }
            });
        });
    </script>


    <script>
        $(function() {

            // Helpers UI
            function handleNoStock(message) {
                Swal.fire({
                    icon: 'info',
                    title: 'Sin stock disponible',
                    html: 'No se encontraron farmacias con stock completo. Puedes editar la receta o elegir compra interna.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#91c845'
                });
            }

            function handleFailure(message) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Error de consulta',
                    html: message || 'Ocurrió un problema consultando el servicio. Intenta nuevamente.',
                    confirmButtonText: 'Aceptar',
                    confirmButtonColor: '#91c845'
                });
            }

            // Cache de nodos
            const $bodyPharmacies = $('#bodyPharmacies');
            const $infoPharmacies = $bodyPharmacies.find('.infoPharmacies');
            const $infoMedications = $bodyPharmacies.find('.infoMedications');
            const $pharmacyField = $('#pharmacyField'); // dropdown de farmacias
            const $searchField = $('#searchPharmacyField');
            const $updateField = $('#updatePharmacyField');
            const $saveField = $('#savePharmacyField');
            const $cancelField = $('#cancelarMedicamentosField');
            const $btnSearch = $('#btnSearchPharmacy');
            const $btnEdit = $('#btnEditMeds');
            const $btnSave = $('#btnSaveMeds');
            const $btnCancel = $('#btnCancelMeds');
            const $saveButtonGlobal = $('#saveButton'); // si existe un botón global de guardar

            const dataVademecumReady = <?php echo json_encode(
                ($medical_prescriptions && $medical_prescriptions->isNotEmpty()
                        ? $medical_prescriptions
                        : $controlled_medications && $controlled_medications->isNotEmpty())
                    ? $controlled_medications
                    : null, 15, 512) ?>;


            // Helper: modo edición ON/OFF
            function toggleEditMode(isEditing) {
                if (isEditing) {
                    $saveField.show();
                    $cancelField.show();
                    $updateField.hide();
                    $searchField.hide();
                    $infoPharmacies.hide();
                    $infoMedications.show();
                    $pharmacyField.hide().removeClass('required');
                    $saveButtonGlobal.addClass('disable_save_button').prop('disabled', true);
                } else {
                    $saveField.hide();
                    $cancelField.hide();
                    $updateField.show();
                    $searchField.show();
                    $infoPharmacies.show();
                    $infoMedications.hide();
                    $pharmacyField.show().addClass('required');
                    $saveButtonGlobal.removeClass('disable_save_button').prop('disabled', false);
                }
            }

            function hydratePrescriptions(prescriptions, opts = {}) {
                const retries = opts.retries ?? 10;
                const delay = opts.delay ?? 200;

                // Normaliza a array 1..n
                const asArray = () => {
                    if (Array.isArray(prescriptions)) {
                        // Si viene 0-based, pásalo a 1-based
                        if (prescriptions.length && prescriptions[0] && !prescriptions[1]) {
                            const arr = [null]; // índice 0 vacío para que arr[1] sea el primero
                            prescriptions.forEach(p => arr.push(p));
                            return arr;
                        }
                        const arr = [null];
                        prescriptions.forEach(p => arr.push(p));
                        return arr;
                    } else {
                        // Objeto con claves "1","2",...
                        const keys = Object.keys(prescriptions).sort((a, b) => Number(a) - Number(b));
                        const arr = [null];
                        keys.forEach(k => arr[Number(k)] = prescriptions[k]);
                        return arr;
                    }
                };
                console.log(asArray());

                const data = asArray();

                function tryHydrate(attempt) {
                    let pending = 0;

                    for (let i = 1; i < data.length; i++) {
                        const el = data[i];
                        if (!el) continue;

                        // Seletores por índice
                        const $mol = $('#rx-molecule-' + i);
                        const $tipo = $('#rx-type-' + i);
                        const $desc = $('#rx-desc-' + i);

                        // Si algún dropdown aún no existe en el DOM, marca pendiente
                        if ($mol.length === 0 || $tipo.length === 0 || $desc.length === 0) {
                            pending++;
                            continue;
                        }

                        // Si existen pero Semantic aún no montó el dropdown (no tiene .dropdown), también reintenta
                        if (typeof $mol.dropdown !== 'function' || typeof $tipo.dropdown !== 'function' ||
                            typeof $desc.dropdown !== 'function') {
                            pending++;
                            continue;
                        }

                        // 1) Mólecula
                        try {
                            $mol.dropdown('set selected', String(el.molecula ?? '').trim());
                        } catch (e) {}

                        // 2) Tipo
                        try {
                            $tipo.dropdown('set selected', String(el.tipo ?? '').trim());
                        } catch (e) {}

                        // 3) Descripción: el menú puede estar vacío (se carga al escribir)
                        try {
                            const text = String(el.descrip ?? '').trim();
                            $desc.dropdown('set text', text);
                            $desc.find('input[type="hidden"]').val(text).trigger('change');
                        } catch (e) {}

                        // 4) Código & Casa (asignación directa)
                        $('textarea[name="codigo-vademecumrx[' + i + ']"]').val(el.codigo ?? '');
                        $('textarea[name="casa-vademecumrx[' + i + ']"]').val(el.casa ?? '');

                        // 5) Opcionales si quieres hidratar todo:
                        if (el.treatment_duration !== undefined)
                            $('textarea[name="rx[' + i + '][treatment_duration]"]').val(el.treatment_duration);
                        if (el.frequency !== undefined)
                            $('textarea[name="rx[' + i + '][frequency]"]').val(el.frequency);
                        if (el.dosage !== undefined)
                            $('textarea[name="rx[' + i + '][dosage]"]').val(el.dosage);
                        if (el.qty_letters !== undefined)
                            $('textarea[name="rx[' + i + '][qty_letters]"]').val(el.qty_letters);
                        if (el.qty_numbers !== undefined)
                            $('textarea[name="rx[' + i + '][qty_numbers]"]').val(el.qty_numbers);
                        if (el.notes !== undefined)
                            $('textarea[name="rx[' + i + '][notes]"]').val(el.notes);
                    }

                    if (pending > 0 && attempt < retries) {
                        setTimeout(() => tryHydrate(attempt + 1), delay);
                    }
                }

                tryHydrate(1);
            }

            // Recolectar recetas del DOM
            function collectPrescriptions() {
                const data = {};
                $('#rx-list .rx-item').each(function() {
                    const idx = $(this).data('index'); // 1..n
                    if (!idx) return;

                    data[idx] = {
                        molecule: $(this).find(`input[name="rx[${idx}][molecule]"]`).val() || '',
                        type: $(this).find(`input[name="rx[${idx}][type]"]`).val() || '',
                        descrip: $(this).find(`input[name="rx[${idx}][descrip]"]`).val() || '',
                        codigo: $(this).find(`textarea[name="codigo-vademecumrx[${idx}]"]`).val() || '',
                        casa: $(this).find(`textarea[name="casa-vademecumrx[${idx}]"]`).val() || '',
                        treatment_duration: $(this).find(
                            `textarea[name="rx[${idx}][treatment_duration]"]`).val() || '',
                        frequency: $(this).find(`textarea[name="rx[${idx}][frequency]"]`).val() || '',
                        dosage: $(this).find(`textarea[name="rx[${idx}][dosage]"]`).val() || '',
                        qty_letters: $(this).find(`textarea[name="rx[${idx}][qty_letters]"]`).val() ||
                            '',
                        qty_numbers: $(this).find(`textarea[name="rx[${idx}][qty_numbers]"]`).val() ||
                            '',
                        notes: $(this).find(`textarea[name="rx[${idx}][notes]"]`).val() || ''
                    };
                });
                return data;
            }

            // Eventos
            $btnEdit.on('click', function() {
                toggleEditMode(true);
                hydratePrescriptions(dataVademecumReady, {
                    retries: 15,
                    delay: 200
                });

            });

            $btnCancel.on('click', function() {
                toggleEditMode(false);
            });

            $btnSave.on('click', function() {
                // Evitar doble click
                if ($btnSave.prop('disabled')) return;
                $btnSave.addClass('loading disabled');

                Swal.fire({
                    title: 'Actualizando medicamentos',
                    html: 'Por favor espera mientras actualizamos los medicamentos…',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => Swal.showLoading()
                });

                const prescriptions = collectPrescriptions();

                $.ajax({
                    url: "<?php echo e(secure_url('/servicio/' . $medication_service->activity_id . '/medication_services/saveMedicaments')); ?>",
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        prescriptions
                    },
                    success: function(resp) {
                        Swal.close();
                        Swal.fire({
                            icon: 'success',
                            title: 'Guardado',
                            text: 'Los medicamentos se guardaron correctamente'
                        });
                        // volver a modo no edición
                        toggleEditMode(false);
                        // si quieres recargar:
                        loadingMain(true);
                        window.location.reload();
                    },
                    error: function(xhr) {
                        Swal.close();
                        const msg = (xhr.responseJSON && xhr.responseJSON.message) ||
                            'No fue posible guardar los medicamentos';
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: msg
                        });
                    },
                    complete: function() {
                        $btnSave.removeClass('loading disabled');
                    }
                });
            });

            // Si en tu flujo inicial corresponde, muestra el botón de editar/guardar:
            // p.ej: si ya hay resultados de stock, probablemente quieras:
            // $updateField.show();
            // $searchField.show();

            // Si quieres que “Buscar farmacias” tenga acción:
            $btnSearch.on('click', function() {
                // Bloquea UI y muestra loader
                loadingMain(true);
                Swal.fire({
                    title: 'Consultando farmacias',
                    html: 'Por favor espera mientras obtenemos las farmacias disponibles…',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => Swal.showLoading()
                });

                // Datos base (inyectados desde Blade)
                const medsFromBlade = <?php echo json_encode($medications ?? [], 15, 512) ?>;

                // Validación mínima: hay medicamentos
                if (!Array.isArray(medsFromBlade) || medsFromBlade.length === 0) {
                    loadingMain(false);
                    Swal.fire({
                        icon: 'warning',
                        title: 'No hay medicamentos para consultar',
                        html: 'No se puede buscar farmacias sin medicamentos.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#91c845'
                    });
                    return;
                }

                // Validar ubicación
                const province = $('#province_medical_prescription').val();
                const canton = $('#canton_medical_prescription').val();
                const district = $('#district_medical_prescription').val();

                if (!province || !canton || !district) {
                    loadingMain(false);
                    Swal.fire({
                        icon: 'warning',
                        title: 'Faltan datos de ubicación',
                        html: 'Selecciona provincia, cantón y distrito para buscar farmacias cercanas.',
                        confirmButtonText: 'Aceptar',
                        confirmButtonColor: '#91c845'
                    });
                    return;
                }

                // Armar payload (asegurando números)
                const data = {
                    items: medsFromBlade.map(med => ({
                        codArticulo: String(med.codigo || '').trim(),
                        quantity: Number(med.quantity_numbers || 0) // fuerza number
                    })),
                    location: {
                        province: String(province).trim(),
                        canton: String(canton).trim(),
                        district: String(district).trim()
                    }
                };

                $.ajax({
                    url: "<?php echo e(secure_url('/servicio/' . $medication_service->activity_id . '/medication_services/getPharmacies')); ?>",
                    method: "POST",
                    // Enviamos como application/x-www-form-urlencoded (Laravel lo mapea bien a arrays anidados)
                    data,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.close();

                        // Normaliza data de sucursales
                        let sucursales = [];
                        if (response && response.success === true) {
                            if (Array.isArray(response.data)) {
                                sucursales = response.data;
                            } else if (response.data && Array.isArray(response.data
                                    .sucursales)) {
                                sucursales = response.data.sucursales;
                            }

                            if (sucursales.length > 0) {
                                // Muestra tabla y marca estado
                                $('#pharmaciesTable').show();
                                $('.medicationState').text('Disponible');

                                // Llena dropdown de sucursales
                                const $menu = $("#pharmacyDropdown .menu").empty();
                                sucursales.forEach(pharmacy => {
                                    const valueObj = {
                                        id: pharmacy.sucursal_id,
                                        name: pharmacy.sucursal_name,
                                        cadena: pharmacy.cadena
                                    };
                                    $menu.append(
                                        `<div class="item"
                    data-value='${JSON.stringify(valueObj)}'
                    data-text="${pharmacy.sucursal_name}">
                 ${pharmacy.sucursal_name} - ${pharmacy.cadena_name ?? ''}
               </div>`
                                    );
                                });

                                // Inicializa/Refresca dropdown
                                $('#pharmacyField').show().addClass('required');
                                $('#pharmacyDropdown')
                                    .dropdown('destroy')
                                    .dropdown({
                                        fullTextSearch: true,
                                        ignoreDiacritics: true,
                                        onChange: function(value, text, $selectedItem) {
                                            if ($selectedItem && $selectedItem.length) {
                                                try {
                                                    const parsed = JSON.parse(
                                                        $selectedItem.attr(
                                                            'data-value') || '{}');
                                                    $('#pharmacy_id').val(parsed.id ||
                                                        '');
                                                    $('#pharmacy_name').val(parsed
                                                        .name || '');
                                                    $('#pharmacy_chain').val(parsed
                                                        .cadena || '');
                                                } catch (e) {
                                                    console.warn(
                                                        'No se pudo parsear data-value de la sucursal:',
                                                        e);
                                                }
                                            }
                                        }
                                    })
                                    .dropdown('refresh');

                                return; // listo
                            }

                            // success: true pero sin sucursales => tratar como NO_STOCK
                            handleNoStock(response.message || 'No hay stock disponible.');
                            $('#pharmacyField').hide().removeClass('required');
                            $('#updatePharmacyField').show();
                            return;
                        }

                        // success === false (HTTP 200) — mirar si es NO_STOCK
                        if (response && response.code === 'NO_STOCK') {
                            handleNoStock(response.message || 'No hay stock disponible.');
                            $('#pharmacyField').hide().removeClass('required');
                            $('#updatePharmacyField').show();
                            return;
                        }

                        // Falla genérica
                        handleFailure((response && response.message) ||
                            'No fue posible obtener las farmacias.');
                    },
                    error: function(xhr) {
                        Swal.close();

                        const res = xhr.responseJSON;
                        if (res && res.code === 'NO_STOCK') {
                            handleNoStock(res.message || 'No hay stock disponible.');
                            $('#updatePharmacyField').show();
                            return;
                        }

                        handleFailure((res && res.message) ||
                            'Error al consultar el servicio Dokka. Intenta nuevamente.');
                    },
                    complete: function() {
                        loadingMain(false);
                    }
                });
            });

        });
    </script>
<?php $__env->stopPush(); ?>
