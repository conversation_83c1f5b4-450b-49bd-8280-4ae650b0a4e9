
$(document).ready(function () {
  // JSON de Costa Rica
  let costarica = {};

  // Carga el JSON de las provincias
  $.getJSON("/js/costarica.json", function (json) {
    costarica = json["province"];

    // Itera sobre cada grupo de dropdowns
    $(".location-group").each(function () {
      const $group = $(this);

      const $provinceDropdown = $group.find(".province");
      const $cantonDropdown = $group.find(".canton");
      const $districtDropdown = $group.find(".district");

      // Obtener los valores preseleccionados de los inputs ocultos usando las clases
      const preselectedProvince = $group.find("input.province_value").val();
      const preselectedCanton = $group.find("input.canton_value").val();
      const preselectedDistrict = $group.find("input.district_value").val();

      // Inicializa los dropdowns
      $provinceDropdown.dropdown();
      $cantonDropdown.dropdown();
      $districtDropdown.dropdown();

      // Poblar el dropdown de provincias y seleccionar el valor preestablecido
      populateDropdown($provinceDropdown, costarica, preselectedProvince);

      // Manejar cambios en la selección de provincia
      $provinceDropdown.on('change', function () {
        const province = $(this).dropdown("get value");
        populateCantons(province, $group);
      });

      // Manejar cambios en la selección de cantón
      $cantonDropdown.on('change', function () {
        const canton = $(this).dropdown("get value");
        const province = $provinceDropdown.dropdown("get value");
        if (canton && province) {
          populateDistricts(province, canton, $group);
        }
      });

      // Poblar cantones y seleccionar el cantón y distrito preestablecido
      populateCantons(preselectedProvince, $group, preselectedCanton, preselectedDistrict);
    });
  });

  // Función para capitalizar la primera letra de los nombres propios y dejar en minúsculas los conectores
  function capitalizeWordsWithExceptionsSecond(str) {
    const exceptions = ['de', 'y', 'la', 'el', 'los', 'las', 'un', 'una', 'por', 'para', 'en', 'con'];

    return str.split(' ').map((word, index) => {
      // Capitaliza la primera palabra o si no está en la lista de excepciones
      if (index === 0 || !exceptions.includes(word.toLowerCase())) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }
      // Deja en minúsculas las excepciones
      return word.toLowerCase();
    }).join(' ');
  }

  // Función para poblar dropdowns y seleccionar el valor preestablecido si existe
  function populateDropdown(dropdown, items, preselectedValue = null) {
    dropdown.dropdown("clear");
    dropdown.find(".menu").empty();

    // Poblamos el dropdown con las opciones
    items.forEach(item => {
      const itemName = capitalizeWordsWithExceptionsSecond(item.name);
      dropdown.find(".menu").append(
        `<div class="item" data-value="${item.code}">${itemName}</div>`
      );
    });

    dropdown.dropdown("refresh");

    // Establece el valor preseleccionado, si existe
    if (preselectedValue) {
      dropdown.dropdown('set selected', preselectedValue);
    }
  }

  // Función para poblar cantones según la provincia seleccionada y selecciona el cantón y distrito preestablecido si es necesario
  function populateCantons(provinceCode, $group, preselectedCanton = null, preselectedDistrict = null) {
    const $cantonDropdown = $group.find(".canton");
    const $districtDropdown = $group.find(".district");

    const province = costarica.find(p => p.code === provinceCode);
    if (province) {
      populateDropdown($cantonDropdown, province.cantons, preselectedCanton);

      const canton = preselectedCanton || (province.cantons[0] ? province.cantons[0].code : null);
      if (canton) {
        $cantonDropdown.dropdown('set selected', canton);
        // Selecciona el primer distrito del cantón seleccionado
        if (preselectedDistrict) {
          populateDistricts(provinceCode, canton, $group, preselectedDistrict);
        } else {
          const firstDistrict = province.cantons.find(c => c.code === canton).districts[0];
          const districtToSelect = firstDistrict ? firstDistrict.code : null;
          populateDistricts(provinceCode, canton, $group, districtToSelect);
        }
      }
    }
  }

  // Función para poblar distritos según el cantón seleccionado y selecciona el distrito preestablecido si es necesario
  function populateDistricts(provinceCode, cantonCode, $group, preselectedDistrict = null) {
    const $districtDropdown = $group.find(".district");

    const province = costarica.find(p => p.code === provinceCode);
    const canton = province ? province.cantons.find(c => c.code === cantonCode) : null;
    if (canton) {
      populateDropdown($districtDropdown, canton.districts, preselectedDistrict);
    }
  }
});




