<div class="title">
    <i class="dropdown icon"></i>
    Receta médica
    <i class="info circle icon rx-help"></i>
</div>

<div class="content" id="rx-form">
    <div class="rx-container">
        <div class="rx-block">
            
            <div class="rx-location location-group">
                <div class="three fields">
                    <div class="field">
                        <label>Provincia</label>
                        <div class="province ui search selection dropdown">
                            <input type="hidden" id="province_medical_prescription" name="province_medical_prescription"
                                class="minus_font province_value"
                                value="<?php echo e(($medication_service->province_medical_prescription ?? $medication_service->province_controlled_medication) ?? ''); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Cantón</label>
                        <div
                            class="canton ui search selection dropdown <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                            <input type="hidden" id="canton_medical_prescription" name="canton_medical_prescription"
                                class="minus_font canton_value"
                                value="<?php echo e(($medication_service->canton_medical_prescription ?? $medication_service->canton_controlled_medication) ?? ''); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>
                    <div class="field">
                        <label>Distrito</label>
                        <div
                            class="district ui search selection dropdown  <?php if($activity->state_id != \App\States\StateMedicationServiceSort::PENDIENTE_SELECCIONAR_FARMACIA): ?> disabled <?php endif; ?>">
                            <input type="hidden" id="district_medical_prescription" name="district_medical_prescription"
                                class="minus_font district_value"
                                value="<?php echo e(($medication_service->district_medical_prescription ?? $medication_service->district_controlled_medication) ?? ''); ?>">
                            <i class="dropdown icon"></i>
                            <div class="default text">Selecciona uno</div>
                            <div class="menu"></div>
                        </div>
                    </div>


                    <div class="two wide field" style="margin-top:25px;">
                        <button type="button" class="ui red small icon fluid button" id="rx-clear-location">
                            <i class="undo icon"></i> Limpiar
                        </button>
                    </div>

                </div>
            </div>

            
            <div class="ui grid" id="rx-list" style="margin-top:15px;">
                <?php $__currentLoopData = $medical_prescriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $m): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $i = $index + 1; ?>

                    <div class="rx-item ui grid" data-index="<?php echo e($i); ?>">
                        <div class="two column row" style="margin-top:15px;">
                            <div class="column">
                                <h4 class="medicine-title" data-alias="receta_medica">Medicamento N° <?php echo e($i); ?></h4>
                            </div>
                            <?php if($i > 1): ?>
                                <div class="column right aligned">
                                    <a class="ui red small icon basic right floated button rx-remove" style="margin-top:5px;">
                                        <i class="remove icon"></i>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        
                        <div class="ui grid vademecum-group" data-index="<?php echo e($i); ?>">
                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Mólecula</label>
                                        <div class="ui search selection dropdown rx-molecule moleculaVademecum"
                                            id="rx-molecule-<?php echo e($i); ?>">
                                            <input type="hidden" name="rx[<?php echo e($i); ?>][molecule]"
                                                value="<?php echo e($m->molecula ?? ''); ?>" class="moleculaVademecum_value"
                                                onchange="handleMoleculaChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona una molécula</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Tipo</label>
                                        <div class="ui search selection dropdown rx-type tipoVademecum "
                                            id="rx-type-<?php echo e($i); ?>">
                                            <input type="hidden" name="rx[<?php echo e($i); ?>][type]" value="<?php echo e($m->tipo ?? ''); ?>"
                                                class="tipoVademecum_value"
                                                onchange="handleTipoChange($(this).closest('.vademecum-group'))">
                                            <i class="dropdown icon"></i>
                                            <div class="default text">Selecciona un tipo</div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Descripción</label>
                                        <div class="ui search selection dropdown rx-desc descripVademecumDropdown "
                                            id="rx-desc-<?php echo e($i); ?>">
                                            <input type="hidden" name="rx[<?php echo e($i); ?>][descrip]"
                                                value="<?php echo e($m->descrip ?? ''); ?>">
                                            <i class="dropdown icon"></i>
                                            <div class="default text"></div>
                                            <div class="menu"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="required field">
                                        <label>Código</label>
                                        <textarea class="textarea-expand" name="codigo-vademecumrx[<?php echo e($i); ?>]"
                                            readonly><?php echo e($m->codigo ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="column">
                                    <div class="field">
                                        <label>Casa</label>
                                        <textarea class="textarea-expand" name="casa-vademecumrx[<?php echo e($i); ?>]"
                                            readonly><?php echo e($m->casa ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="required field">
                                        <label>Duración del tratamiento</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][treatment_duration]"
                                            rows="2"><?php echo e($m->treatment_duration ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Frecuencia</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][frequency]"
                                            rows="2"><?php echo e($m->frequency ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Dosis / vía</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][dosage]"
                                            rows="2"><?php echo e($m->dosage ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Cantidad (letras)</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][qty_letters]"
                                            rows="2"><?php echo e($m->quantity_letters ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="three column row">
                                <div class="column">
                                    <div class="field">
                                        <label>Cantidad (números)</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][qty_numbers]" rows="2"
                                            oninput="this.value=this.value.replace(/[^0-9]/g,'');"><?php echo e($m->quantity_numbers ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="field">
                                        <label>Notas</label>
                                        <textarea class="auto-resize" name="rx[<?php echo e($i); ?>][notes]"
                                            rows="2"><?php echo e($m->notes ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="fields">
                <div class="field two wide required" style="margin-top:25px;">
                    <a class="ui basic small icon blue fluid button" id="rx-dup">
                        <i class="add icon"></i>Agregar
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        // (1) Inicializa dropdowns SOLO en el scope pasado
        function initDropdowns($scope) {
            ($scope || $(document)).find('.ui.selection.dropdown').dropdown({
                fullTextSearch: true,
                ignoreDiacritics: true
            });
        }

        // (2) Siguiente índice
        function nextIndex() {
            const $list = $('#rx-list');
            const last = $list.find('.rx-item:last').data('index') || 0;
            return last + 1;
        }

        // (3) Reindexa names/ids y título
        function reindexNode($node, oldIndex, newIndex) {
            $node.attr('data-index', newIndex);
            $node.find('.medicine-title').text('Medicamento N° ' + newIndex);

            // name="rx[old][...]" -> "rx[new][...]"
            $node.find('input[name], textarea[name], select[name]').each(function () {
                const name = $(this).attr('name');
                if (name) $(this).attr('name', name.replace(/\[\d+\]/, '[' + newIndex + ']'));
            });

            // id="...-old" -> "...-new" (si termina en -número)
            $node.find('[id]').each(function () {
                const id = $(this).attr('id');
                if (id && /-\d+$/.test(id)) $(this).attr('id', id.replace(/-\d+$/, '-' + newIndex));
            });
        }

        // (4) Limpia valores del clon
        function clearValues($node) {
            $node.find('textarea').val('');
            $node.find('input[type="text"], input[type="hidden"]').val('');
            $node.find('.ui.selection.dropdown').each(function () {
                // quita clases de estado que puedan venir copiadas
                $(this).removeClass('active visible filtered');
                try { $(this).dropdown('clear'); } catch (e) { }
            });
        }

        // (5) Agregar (clonar “sin eventos” y re-inicializar)
        $('#rx-dup').on('click', function () {
            const $list = $('#rx-list');
            const $last = $list.find('.rx-item:last');
            if (!$last.length) return;

            const oldIndex = $last.data('index') || 0;
            const newIndex = nextIndex();

            // ❗ Clon limpio (sin eventos): usar outerHTML → jQuery crea nuevos nodos sin handlers
            const $clone = $($last.prop('outerHTML'));

            reindexNode($clone, oldIndex, newIndex);
            clearValues($clone);

            // Asegura que cualquier estado de dropdown no se herede
            $clone.find('.ui.dropdown').each(function () {
                try { $(this).dropdown('destroy'); } catch (e) { }
                $(this).removeClass('active visible filtered');
            });

            $list.append($clone);
            initDropdowns($clone); // Inicializa SOLO el clon
        });

        // (6) Eliminar item
        $(document).on('click', '.rx-remove', function () {
            $(this).closest('.rx-item').remove();
        });

        // (7) Limpia ubicación
        $('#rx-clear-location').on('click', function () {
            $('.province input[type=hidden], .canton input[type=hidden], .district input[type=hidden]').val('');
            $('.province, .canton, .district').dropdown('clear');
        });

        // (8) Reemplaza los inline onchange por delegados (evita handlers duplicados)
        $(document).on('change', '.moleculaVademecum_value', function () {
            handleMoleculaChange($(this).closest('.vademecum-group'));
        });
        $(document).on('change', '.tipoVademecum_value', function () {
            handleTipoChange($(this).closest('.vademecum-group'));
        });

        // Inicializa los existentes al cargar
        initDropdowns();




    });

    $(document).ready(function () {
        setTimeout(function () {
           
        }, 11000); // espera 1s para que Semantic UI cargue los menús
    });


</script>