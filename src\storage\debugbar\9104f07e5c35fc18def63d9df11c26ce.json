{"__meta": {"id": "9104f07e5c35fc18def63d9df11c26ce", "datetime": "2025-09-16 23:17:15", "utime": 1758086235.915749, "method": "GET", "uri": "/obtenerLista/vademecum", "ip": "**********"}, "php": {"version": "7.2.34", "interface": "fpm-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758086202.939343, "end": 1758086235.915817, "duration": 32.97647404670715, "duration_str": "32.98s", "measures": [{"label": "Booting", "start": 1758086202.939343, "relative_start": 0, "end": 1758086229.338706, "relative_end": 1758086229.338706, "duration": 26.399363040924072, "duration_str": "26.4s", "params": [], "collector": null}, {"label": "Application", "start": 1758086225.093888, "relative_start": 22.154545068740845, "end": 1758086235.915819, "relative_end": 1.9073486328125e-06, "duration": 10.821930885314941, "duration_str": "10.82s", "params": [], "collector": null}]}, "memory": {"peak_usage": 4194304, "peak_usage_str": "4MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET obtenerLista/vademecum", "middleware": "web, App\\Http\\Middleware\\CheckClientAccess, renew.password", "domain": "{cpath}.renapp.com", "controller": "App\\Http\\Controllers\\Vademecum\\VademecumController@index", "namespace": "App\\Http\\Controllers", "prefix": null, "where": [], "file": "app/Http/Controllers/Vademecum/VademecumController.php:26-33"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.77162, "accumulated_duration_str": "771.62ms", "statements": [{"sql": "select * from `users` where `id` = '18' limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 46, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 49, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 55, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 70, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.16727, "duration_str": "167.27ms", "stmt_id": "/app/Http/Middleware/RequestLoggerMiddleware.php:28", "connection": "ebdb"}, {"sql": "select * from `clients` where `path` = 'oceanica-qa' limit 1", "type": "query", "params": [], "bindings": ["oceanica-qa"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 22}, {"index": 48, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 51, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 57, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 72, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13216999999999998, "duration_str": "132.17ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:22", "connection": "ebdb"}, {"sql": "select * from `user_clients` where `client_id` = '3' and `user_id` = '18' limit 1", "type": "query", "params": [], "bindings": ["3", "18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "backtrace": [{"index": 13, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 25}, {"index": 47, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 50, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 56, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 71, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.13855, "duration_str": "138.55ms", "stmt_id": "/app/Http/Middleware/CheckClientAccess.php:25", "connection": "ebdb"}, {"sql": "select * from `vademecums`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "backtrace": [{"index": 14, "namespace": null, "name": "/app/Http/Controllers/Vademecum/VademecumController.php", "line": 29}, {"index": 22, "namespace": "middleware", "name": "renew.password", "line": 31}, {"index": 25, "namespace": null, "name": "/app/Http/Middleware/CheckClientAccess.php", "line": 32}, {"index": 59, "namespace": null, "name": "/app/Http/Middleware/RequestLoggerMiddleware.php", "line": 28}, {"index": 62, "namespace": null, "name": "/app/Http/Middleware/HSTSMiddleware.php", "line": 18}, {"index": 68, "namespace": null, "name": "/app/Http/Middleware/FrameGuard.php", "line": 18}, {"index": 83, "namespace": null, "name": "/public/index.php", "line": 53}], "duration": 0.33363, "duration_str": "333.63ms", "stmt_id": "/app/Http/Controllers/Vademecum/VademecumController.php:29", "connection": "ebdb"}]}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"wilmer<PERSON>za10\"\n  \"user\" => array:50 [\n    \"id\" => 18\n    \"user_type\" => \"\"\n    \"email\" => \"<EMAIL>\"\n    \"affiliate_id\" => null\n    \"created_at\" => \"2024-09-17 10:51:15\"\n    \"updated_at\" => \"2025-09-15 13:28:34\"\n    \"full_name\" => \"WILMERR MAZA BANDA\"\n    \"first_name\" => \"WILMERR\"\n    \"last_name\" => \"MAZA BANDA\"\n    \"username\" => \"wilmermaza10\"\n    \"photo\" => \"user_photo/at8RH2yiiVxOjsYpnJozCU3S7UrqTy5O4T49x4i8.jpeg\"\n    \"area_id\" => 1\n    \"position_id\" => null\n    \"deleted_at\" => null\n    \"ascribed\" => 0\n    \"old_password1\" => \"$2y$10$AiImbU4Usf3eY8Hz865zbOnsfyZH2PbbPKJdibZUTAz/ywzOFXv.W\"\n    \"old_password2\" => \"$2y$10$cFzxSS3xlT4izkkAK7kze.tBRQ7Jm9N6uh1D4r8gCrLH39uAyhljq\"\n    \"old_password3\" => \"$2y$10$sLHd.4zpr6JC9myNAorkHOxRsO3Oys2y7SG0mbTvN/d5Bo3uYhUoS\"\n    \"last_login\" => \"2025-09-15 13:28:34\"\n    \"next_update\" => \"2030-03-06 03:00:00\"\n    \"extra_data\" => \"\"\n    \"zoom_api_key\" => null\n    \"zoom_api_secret\" => null\n    \"identification_number\" => \"**********\"\n    \"creator\" => null\n    \"has_schedule\" => \"0\"\n    \"company_id\" => null\n    \"department\" => null\n    \"municipality\" => null\n    \"ocupation\" => null\n    \"phone\" => \"**********\"\n    \"doc_type\" => \"CF\"\n    \"medical_record_number\" => null\n    \"license_number\" => null\n    \"rethus\" => null\n    \"signature\" => null\n    \"name_area\" => null\n    \"code_mnk\" => \"000010\"\n    \"brokerage_name\" => \"BCR CORREDORA DE SEGUROS SA\"\n    \"new_business_email\" => \"\"\n    \"advisor_name\" => \"000010\"\n    \"type_inter\" => \"G\"\n    \"correduria\" => null\n    \"code_correduria\" => \"009101\"\n    \"tipo_corredor\" => \"C\"\n    \"provider_id\" => null\n    \"resp_acsel_login\" => null\n    \"tomador_id\" => 0\n    \"unique_code\" => \"651827\"\n    \"autorizados\" => \"12486,42222\"\n  ]\n]", "api": "array:2 [\n  \"name\" => \"Guest\"\n  \"user\" => array:1 [\n    \"guest\" => true\n  ]\n]"}, "names": "web: wilmermaza10"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi", "_previous": "array:1 [\n  \"url\" => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "18", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"format": "html", "content_type": "application/json", "status_text": "OK", "status_code": "200", "request_query": "[]", "request_request": "[]", "request_headers": "array:18 [\n  \"cookie\" => array:1 [\n    0 => \"XSRF-TOKEN=eyJpdiI6Im9Qa2RsTlFMZWFGajh1d3E1Wnd0Smc9PSIsInZhbHVlIjoiajJDSGFBVGpZWFZIbFFYXC9sWTluYVlPSElsbVV5WmlnaHJPT2Z4cGdIaW9TaE5Rc1wvaDRST0FXU3BKZm1CcXpRIiwibWFjIjoiN2Q2ZjZmZTUzOWEzZGMzNWJmMGEzZGM1ZDExMTlhYWJkY2RkYTNmZjViOWY1NDNmYjE0NDU1ODllZDI1OWZkYyJ9; laravel_session_cookie=eyJpdiI6IjNkamJmZlZxZzN0bHBKS2pRNlRwSFE9PSIsInZhbHVlIjoidTFyNHNESHRHdUd3amN3WjdcL2w2OEZXYXdWUmxaS1ptY3BISmhQVEJFdTdIcGx6YXBcL25RQnFTakRDWGhGMlwveUd0UEVwZlVOMFNackNlcEp5OEFoNFl6UHMwS3ZMcERpOEFYR3VWTElBOG5GalpZRTNBa0EyY0J1Z3gwbmxDbTYiLCJtYWMiOiJmMjI4ZmI0NDJiZTM2NjE0ZDIxY2YwNzFmOGYzMWFhNWNkZTA1N2Y3YTkyMjk5NTdmYmY1NWRhMGQ4YWE3NGVhIn0%3D\"\n  ]\n  \"accept-language\" => array:1 [\n    0 => \"en,es-ES;q=0.9,es;q=0.8\"\n  ]\n  \"accept-encoding\" => array:1 [\n    0 => \"gzip, deflate, br, zstd\"\n  ]\n  \"referer\" => array:1 [\n    0 => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n  ]\n  \"sec-fetch-dest\" => array:1 [\n    0 => \"empty\"\n  ]\n  \"sec-fetch-mode\" => array:1 [\n    0 => \"cors\"\n  ]\n  \"sec-fetch-site\" => array:1 [\n    0 => \"same-origin\"\n  ]\n  \"sec-ch-ua-mobile\" => array:1 [\n    0 => \"?0\"\n  ]\n  \"sec-ch-ua\" => array:1 [\n    0 => \"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"\"\n  ]\n  \"accept\" => array:1 [\n    0 => \"*/*\"\n  ]\n  \"user-agent\" => array:1 [\n    0 => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36\"\n  ]\n  \"x-requested-with\" => array:1 [\n    0 => \"XMLHttpRequest\"\n  ]\n  \"x-csrf-token\" => array:1 [\n    0 => \"aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi\"\n  ]\n  \"sec-ch-ua-platform\" => array:1 [\n    0 => \"\"Windows\"\"\n  ]\n  \"connection\" => array:1 [\n    0 => \"keep-alive\"\n  ]\n  \"host\" => array:1 [\n    0 => \"oceanica-qa.renapp.com\"\n  ]\n  \"content-length\" => array:1 [\n    0 => \"\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"\"\n  ]\n]", "request_server": "array:62 [\n  \"PHP_EXTRA_CONFIGURE_ARGS\" => \"--enable-fpm --with-fpm-user=www-data --with-fpm-group=www-data --disable-cgi\"\n  \"LANGUAGE\" => \"es_ES.UTF-8\"\n  \"HOSTNAME\" => \"b44486792d1f\"\n  \"PHP_INI_DIR\" => \"/usr/local/etc/php\"\n  \"HOME\" => \"/var/www\"\n  \"PHP_LDFLAGS\" => \"-Wl,-O1 -pie\"\n  \"PHP_CFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_VERSION\" => \"7.2.34\"\n  \"GPG_KEYS\" => \"1729F83938DA44E27BA0F4D3DBDB397470D12172 B1B44D8F021E4E2D6021E995DC9FF8D3EE5AF27F\"\n  \"PHP_CPPFLAGS\" => \"-fstack-protector-strong -fpic -fpie -O2 -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64\"\n  \"PHP_ASC_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz.asc\"\n  \"PHP_URL\" => \"https://www.php.net/distributions/php-7.2.34.tar.xz\"\n  \"PATH\" => \"/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\"\n  \"LANG\" => \"es_ES.UTF-8\"\n  \"PHPIZE_DEPS\" => \"autoconf \\t\\tdpkg-dev \\t\\tfile \\t\\tg++ \\t\\tgcc \\t\\tlibc-dev \\t\\tmake \\t\\tpkg-config \\t\\tre2c\"\n  \"LC_ALL\" => \"es_ES.UTF-8\"\n  \"PWD\" => \"/var/www/html\"\n  \"PHP_SHA256\" => \"409e11bc6a2c18707dfc44bc61c820ddfd81e17481470f3405ee7822d8379903\"\n  \"USER\" => \"www-data\"\n  \"HTTP_COOKIE\" => \"XSRF-TOKEN=eyJpdiI6Im9Qa2RsTlFMZWFGajh1d3E1Wnd0Smc9PSIsInZhbHVlIjoiajJDSGFBVGpZWFZIbFFYXC9sWTluYVlPSElsbVV5WmlnaHJPT2Z4cGdIaW9TaE5Rc1wvaDRST0FXU3BKZm1CcXpRIiwibWFjIjoiN2Q2ZjZmZTUzOWEzZGMzNWJmMGEzZGM1ZDExMTlhYWJkY2RkYTNmZjViOWY1NDNmYjE0NDU1ODllZDI1OWZkYyJ9; laravel_session_cookie=eyJpdiI6IjNkamJmZlZxZzN0bHBKS2pRNlRwSFE9PSIsInZhbHVlIjoidTFyNHNESHRHdUd3amN3WjdcL2w2OEZXYXdWUmxaS1ptY3BISmhQVEJFdTdIcGx6YXBcL25RQnFTakRDWGhGMlwveUd0UEVwZlVOMFNackNlcEp5OEFoNFl6UHMwS3ZMcERpOEFYR3VWTElBOG5GalpZRTNBa0EyY0J1Z3gwbmxDbTYiLCJtYWMiOiJmMjI4ZmI0NDJiZTM2NjE0ZDIxY2YwNzFmOGYzMWFhNWNkZTA1N2Y3YTkyMjk5NTdmYmY1NWRhMGQ4YWE3NGVhIn0%3D\"\n  \"HTTP_ACCEPT_LANGUAGE\" => \"en,es-ES;q=0.9,es;q=0.8\"\n  \"HTTP_ACCEPT_ENCODING\" => \"gzip, deflate, br, zstd\"\n  \"HTTP_REFERER\" => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n  \"HTTP_SEC_FETCH_DEST\" => \"empty\"\n  \"HTTP_SEC_FETCH_MODE\" => \"cors\"\n  \"HTTP_SEC_FETCH_SITE\" => \"same-origin\"\n  \"HTTP_SEC_CH_UA_MOBILE\" => \"?0\"\n  \"HTTP_SEC_CH_UA\" => \"\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"\"\n  \"HTTP_ACCEPT\" => \"*/*\"\n  \"HTTP_USER_AGENT\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36\"\n  \"HTTP_X_REQUESTED_WITH\" => \"XMLHttpRequest\"\n  \"HTTP_X_CSRF_TOKEN\" => \"aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi\"\n  \"HTTP_SEC_CH_UA_PLATFORM\" => \"\"Windows\"\"\n  \"HTTP_CONNECTION\" => \"keep-alive\"\n  \"HTTP_HOST\" => \"oceanica-qa.renapp.com\"\n  \"PATH_INFO\" => \"\"\n  \"SCRIPT_FILENAME\" => \"/var/www/html/public/index.php\"\n  \"REDIRECT_STATUS\" => \"200\"\n  \"SERVER_NAME\" => \"oceanica.renapp.us\"\n  \"SERVER_PORT\" => \"443\"\n  \"SERVER_ADDR\" => \"**********\"\n  \"REMOTE_PORT\" => \"34834\"\n  \"REMOTE_ADDR\" => \"**********\"\n  \"SERVER_SOFTWARE\" => \"nginx/1.26.3\"\n  \"GATEWAY_INTERFACE\" => \"CGI/1.1\"\n  \"HTTPS\" => \"on\"\n  \"REQUEST_SCHEME\" => \"https\"\n  \"SERVER_PROTOCOL\" => \"HTTP/1.1\"\n  \"DOCUMENT_ROOT\" => \"/var/www/html/public\"\n  \"DOCUMENT_URI\" => \"/index.php\"\n  \"REQUEST_URI\" => \"/obtenerLista/vademecum\"\n  \"SCRIPT_NAME\" => \"/index.php\"\n  \"CONTENT_LENGTH\" => \"\"\n  \"CONTENT_TYPE\" => \"\"\n  \"REQUEST_METHOD\" => \"GET\"\n  \"QUERY_STRING\" => \"\"\n  \"FCGI_ROLE\" => \"RESPONDER\"\n  \"PHP_SELF\" => \"/index.php\"\n  \"REQUEST_TIME_FLOAT\" => 1758086202.9393\n  \"REQUEST_TIME\" => 1758086202\n  \"argv\" => []\n  \"argc\" => 0\n]", "request_cookies": "array:2 [\n  \"XSRF-TOKEN\" => null\n  \"laravel_session_cookie\" => \"nuE9DDo4EAWK7Fki1KZCUv5MCeA3itw4PrAmTYsH\"\n]", "response_headers": "array:5 [\n  \"cache-control\" => array:1 [\n    0 => \"no-cache, private\"\n  ]\n  \"date\" => array:1 [\n    0 => \"Wed, 17 Sep 2025 05:17:15 GMT\"\n  ]\n  \"content-type\" => array:1 [\n    0 => \"application/json\"\n  ]\n  \"set-cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6InpGZWFnS1JTWE9vNDlVQ1dHRnZOUnc9PSIsInZhbHVlIjoiR21jR1wvR1kyY1owNVVxbEZOa1ptSEVVN0xVZWNaZ2ZoOGJtZnh2OHNSVHkrQUN3QmRCTGpKZ0xcL0FQR2VRYjI4IiwibWFjIjoiOTVhN2RkMTZmMzA1MWVmM2M0MWFiNTljMTE2MWFjYTNhYjI0MGNhZGEyMGJlMGE2NWEwNjIzMDIzOWMxNjdjMyJ9; expires=Thu, 18-Sep-2025 01:17:15 GMT; Max-Age=72000; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6ImY0bEZjYmJocjV6djllM1F0UXJIbWc9PSIsInZhbHVlIjoiUnlzWDB1a1psNmZ1VzFEdVZFMndickR1YzVYcng3ZStSM1wvalFWR1REMEZJcmoxRXZZMzZ0Tithc1VFbnpxdkNoc3lKUnlEQ2o3VU9KWmh3OURmUWI3UHVTejZKaERpZkdyN2Z6QmFjRndIMkw0MWNxV0RjUDNWODBQOTBVTnc1IiwibWFjIjoiMDE3ZDUxZWE2YjI4Yzg0N2RkMDMxM2YyNTk5MjdmYzdmMDIxMzY4ZmNkZWE4MjZmNjM3NzJkNmFmOTQyYzA3ZiJ9; expires=Thu, 18-Sep-2025 01:17:15 GMT; Max-Age=72000; path=/; httponly\"\n  ]\n  \"Set-Cookie\" => array:2 [\n    0 => \"XSRF-TOKEN=eyJpdiI6InpGZWFnS1JTWE9vNDlVQ1dHRnZOUnc9PSIsInZhbHVlIjoiR21jR1wvR1kyY1owNVVxbEZOa1ptSEVVN0xVZWNaZ2ZoOGJtZnh2OHNSVHkrQUN3QmRCTGpKZ0xcL0FQR2VRYjI4IiwibWFjIjoiOTVhN2RkMTZmMzA1MWVmM2M0MWFiNTljMTE2MWFjYTNhYjI0MGNhZGEyMGJlMGE2NWEwNjIzMDIzOWMxNjdjMyJ9; expires=Thu, 18-Sep-2025 01:17:15 GMT; path=/\"\n    1 => \"laravel_session_cookie=eyJpdiI6ImY0bEZjYmJocjV6djllM1F0UXJIbWc9PSIsInZhbHVlIjoiUnlzWDB1a1psNmZ1VzFEdVZFMndickR1YzVYcng3ZStSM1wvalFWR1REMEZJcmoxRXZZMzZ0Tithc1VFbnpxdkNoc3lKUnlEQ2o3VU9KWmh3OURmUWI3UHVTejZKaERpZkdyN2Z6QmFjRndIMkw0MWNxV0RjUDNWODBQOTBVTnc1IiwibWFjIjoiMDE3ZDUxZWE2YjI4Yzg0N2RkMDMxM2YyNTk5MjdmYzdmMDIxMzY4ZmNkZWE4MjZmNjM3NzJkNmFmOTQyYzA3ZiJ9; expires=Thu, 18-Sep-2025 01:17:15 GMT; path=/; httponly\"\n  ]\n]", "path_info": "/obtenerLista/vademecum", "session_attributes": "array:5 [\n  \"_token\" => \"aH3oucDQPljrF6OM8GgxWKiYscgEhtWEUG9NYZYi\"\n  \"_previous\" => array:1 [\n    \"url\" => \"https://oceanica-qa.renapp.com/servicio/518948/medication_services\"\n  ]\n  \"_flash\" => array:2 [\n    \"old\" => []\n    \"new\" => []\n  ]\n  \"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d\" => 18\n  \"PHPDEBUGBAR_STACK_DATA\" => []\n]"}}