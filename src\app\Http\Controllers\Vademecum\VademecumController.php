<?php

namespace App\Http\Controllers\Vademecum;

use App\Http\Controllers\Controller;
use App\Vademecum;

class VademecumController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    // Lista de vademecum
    public function index()
    {
        // Obtener vademecum de la base de datos
        $vademecum = Vademecum::get();

        // devolver los datos en formato JSON
        return response()->json(['List' => $vademecum]);
    }

}